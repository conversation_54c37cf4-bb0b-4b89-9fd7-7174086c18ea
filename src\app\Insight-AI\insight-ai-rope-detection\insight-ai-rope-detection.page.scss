.backend {
    margin-left: 0px !important;
    // border: 1px solid black;
    width: 183px !important;
}

ion-card {
    margin: 0px !important;
}

.canvasOutput {
    display:flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    justify-items: center;
    margin-top:15px;
}

.rope {
    // margin-left: 5px !important;
    // margin-right:5px !important;
    // margin-top: -30px !important;
    // margin-bottom: -30px !important;
    max-width: 14rem;
    max-height: 14rem;
}

.actionBtns {
    display: flex;
    justify-content: space-around;
    gap: 20px;
    margin-bottom:0px;
    margin-top:15px;
}

.actionBtns > img {
    height: 35px;
}

.actionBtns img:nth-child(1) {
    margin-left: 10px;
    margin-right: 10px;
}

.mainDiv {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

#imgDiv {
    justify-content: center;
    display: flex;
    align-items: center;
    margin-top: 15px;
    margin-bottom: 10px;
}

.ropeImage {
    // width:300px;
    // height:300px;
    object-fit :contain;
}

@media only screen and (max-width: 764px) {
    #compMsg {
        color:red;
    }
}

.scrollBtn {
    height:30px;
}

@media only screen and (min-width:481px) {
    .insightAIWarning {
        margin: auto !important;
        --height: 90% !important;
        --width: 90% !important;
        --border-radius:20px
    }
}