<ion-header #header class="ion-no-border" collapse="fade">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: var(--ion-color-primary);" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Insight-quick-inspect' | translate}}</ion-title>
  </ion-toolbar>
  <ion-toolbar>
    <ion-card #searchCard style="padding:0px;border-radius:20px;background-color: var(--ion-color-primary-light);">
      <ion-searchbar animated class="custom" show-clear-button="always" (ionInput)="getItems($event)" debounce="500"></ion-searchbar>
      @if(HMPECertList.length>0) {
        <p style="font-size: 16px;margin-bottom:0;margin-left:10px;color:var(--ion-color-secondary-contrast)">{{'insightAI_cert_selection_prompt' | translate}}</p>
      }
    </ion-card>
  </ion-toolbar>
</ion-header>

<ion-content #contentRef>
  <input id="myInput" type="file" *ngIf="device.platform == 'browser' || device.platform=='windows'" style="visibility:hidden; display: none;" accept="image/x-png,image/jpeg,image/jpg,image/png" (change)="onImageChange($event)"/>
  
    <cdk-virtual-scroll-viewport #viewport itemSize="56" minBufferPx="900" maxBufferPx="1350">
      <ion-list>
        <ion-item  *cdkVirtualFor="let options of HMPECertList" (click)="goTOVisionPage(options,guidePrompt)" tappable lines="none" style="border-radius: 20px;box-shadow: #0000003d 0px 3px 8px;margin:5px;">
          <div style="display: block; width: 100%">
              <div style="color: black; width: 100%">
                  {{options.NAME}}
              </div>
              <div *ngIf="options.PRODUCT != '' || options.PRODUCT != null" style="color: grey; width: 100%">
                  {{'Product' | translate}} : {{options.PRODUCT}}
              </div>
              <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                  {{'Diameter' | translate}} : {{options.DIAM}}
              </div>
          </div>
      </ion-item>
      </ion-list>
    </cdk-virtual-scroll-viewport>
  <!-- </div> -->
  <div *ngIf="HMPECertList.length == 0">
    <p>{{'No Certificates Found' | translate}}</p>
  </div>
</ion-content>


<ion-footer class="footer-style" #footer>
  <ion-grid style="text-align:center;">
    <ion-row>
      <ion-col>
        <div (click)="openMenu()">
          <fa-icon class="icon-style" icon="bars"></fa-icon>
        </div>
      </ion-col>
      <!-- <ion-col>
        <div (click)="dataService.navigateToHome()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style"  icon="home"></fa-icon>
        </div>
      </ion-col> -->
      <ion-col>
        <div (click)="goToLineTracker()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="list-check"></fa-icon>
        </div>
      </ion-col>
      <ion-col>
        <div (click)="dataService.navigateToInspection()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/icon/Rope_Inspection_ICON_3A.png" class="bottom-bar-image-style fa-fw">
          <!-- <fa-icon class="icon-style" icon="search"></fa-icon> -->
        </div>
      </ion-col>
      <ion-col>
        <div (click)="dataService.navigateToResources()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="grip"></fa-icon>
        </div>
      </ion-col>
      <ion-col>
        <div (click)="dataService.navigateToContact()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="envelope" class="icon-style"></fa-icon>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-footer>
