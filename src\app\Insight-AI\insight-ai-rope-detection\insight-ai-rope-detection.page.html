<ion-header>
  <ion-toolbar color="primary" color="light">
    <ion-buttons slot="start" *ngIf="screenMode!=undefined && screenMode=='QUICKINSPECT'">
      <ion-button (click)="closeModal()" color="primary">
        <ion-icon name="arrow-back" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="viewTailedImages()"
        *ngIf="visionAIService.showTilesAndSaveTilesEnabled && screenMode!='PREVIEWRESULTS'" color="primary">
        @if(showClaheAndTailedImages) {
        <ion-icon slot="icon-only" name="eye-outline"></ion-icon>
        } @else {
        <ion-icon slot="icon-only" name="eye-off-outline"></ion-icon>
        }
      </ion-button>
      <ion-button *ngIf="screenMode!=undefined && screenMode=='PREVIEWRESULTS'" (click)="closeModal()"
        color="primary">close</ion-button>
    </ion-buttons>
    <ion-title>{{ "INSIGHT-AI-RESULTS" | translate}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="mainDiv" id="insightContainer">
    @if(score!=null) {
    <div class="actionBtns">
      @if(screenMode!=undefined && screenMode!='QUICKINSPECT') {
        @if(screenMode!='PREVIEWRESULTS') {
          <img src="assets/icon/Reject.png" 
            alt="Manual"
            style="height:50px;cursor:pointer;margin-right:8px;"
            (click)="manual()" />
          <img src="assets/icon/Accept.png"
            alt="Accept"
            style="height:50px;cursor:pointer;margin-right:8px;"
            (click)="acceptScore()" />
        }
        <img src="assets/icon/Edit_Pencil.png"
            alt="Retake" 
            style="height:50px;cursor:pointer;"
            (click)="reatakePicture()" />
      }
    </div>
    }

    @if(insightAIcomplete) {
      <div class="canvasOutput">
        <h3 id="score" style="align-self: center;color: rgb(6, 69, 205); margin-bottom:0px;">Result : {{ score }} </h3>
        @if(score <= 2) {
          <h5 style="color: green; font-weight: bold; margin-bottom:20px;">Minimal strength loss (Continue use)</h5>
        } @else if(score > 2 && score < 5) {
          <h5 style="color: orange; font-weight: bold; margin-bottom:20px;">Significant strength loss (Consult Samson)</h5>
        } @else if(score >= 5) {
          <h5 style="color: red; font-weight: bold; margin-bottom:20px;">Severe strength loss (Replace or Retire Rope)</h5>
        }
      </div>
    }

    <div style="display: flex;justify-content: center;align-items: center;flex-direction: column;">
      <!-- ! this image is used to show to the user in landscape mode -->
      <img #ropeImage id="ropeImage" class="rope"/>
      <!-- ! this image is used for AI prediction -->
      <img [hidden]="true" id="ropeSrcImg" class="ropeSrcImage"/>
    </div>

    <div class="canvasOutput" id="output"
      [ngStyle]="{ display: insightAIcomplete && visionAIService.showTilesAndSaveTilesEnabled && showClaheAndTailedImages ? 'block' : 'none' }"
      #histogramOutput style="border:1px solid grey">
      <!-- ! idu greyscale image -->
      <canvas id="ropeHistogramImg"></canvas>
      @if(insightAIcomplete) {
      <p>Image before preprocess 👆</p>
      }
      <!-- ! idu tiled images -->
      <div id="tiledImages" #tiledImages>

      </div>
    </div>

    <div #amsteelComparator
      [ngStyle]="{ display: selectedRopeType === 'Amsteel' ? 'block' : 'none' }"
      style="overflow:auto;white-space: nowrap;width:384px;scroll-behavior: smooth;">
      @for (item of amsteelComparatorImages; track item.id) {
      <img src="{{ item.image }}" style="object-fit:contain;border-radius: 5px;padding-top:12px" />
      } @empty {
      <div>No Images found.</div>
      }
    </div>

    <div #tenexComparator [ngStyle]="{ display: selectedRopeType === 'Tenex' ? 'block' : 'none' }"
      style="overflow:auto;white-space: nowrap;width:384px;scroll-behavior: smooth;">
      @for (item of tenexComparatorImages; track item.id) {
      <img src="{{ item.image }}" style="object-fit:contain;border-radius: 5px;padding-top:12px" />
      } @empty {
      <div>No Images found.</div>
      }
    </div>

    <div style="display: flex;flex-direction: row;justify-content: center;align-items: center;"
      *ngIf="insightAIcomplete">
      <ion-button (click)="scrollLeft()" class="scrollBtn">
        <ion-icon slot="icon-only" name="caret-back-outline" color="light"></ion-icon>
      </ion-button>
      <ion-button (click)="scrollRight()" class="scrollBtn">
        <ion-icon slot="icon-only" name="caret-forward-outline" color="light"></ion-icon>
      </ion-button>
    </div>
  </div>

  @if(insightAIcomplete) {
  <div id="disclaimer" style="display: flex;justify-content: center;align-items: center;">
    <fa-icon id="open-modal" style="margin-right: 5px;color:#0057B3;cursor: pointer; font-size: 27px;"
      icon="circle-info"></fa-icon>
    <p style="margin-top: 1rem;">Disclaimer</p>
    <ion-modal trigger="open-modal" #disclaimerModal class="insightAIWarning">
      <ng-template>
        <ion-header>
          <ion-toolbar>
            <ion-title>Insight AI Disclaimer</ion-title>
            <ion-buttons slot="end">
              <ion-button (click)="disclaimerModal.dismiss()">Close</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <h5 style="padding: 10px; font-weight: bold;">WARNING FOR SAMSON INSPECTION SERVICES</h5>
          <p style="padding: 10px;">
            Samson's service and inspection programs and tools (“Samson Services”) are
            designed to assist in the evaluation of the condition and integrity of Samson Rope
            products (“Products”) with the goal of improving field decisions regarding repair and
            retirement of Products through earlier identification of line damage. However,
            assessment of rope health is inherently imperfect. Samson Services offer an
            important tool to enhance early detection of rope damage and thereby to reduce
            risk. But Samson Services cannot eliminate the inherent risks of rope assessment
            or guarantee that no rope failures or other negative outcomes will occur. Use of
            Samson Products entail unavoidable risks, including risks of death and serious
            personal injury and damage to rope / lines, equipment and other property, even
            when rope care procedures are performed flawlessly. Also, in certain cases, multiple
            forms of damage in proximity over a short length of rope may not account for the
            cumulative effects on rope health of contiguous damage.
            The Insight AI tool included in the Samson Services uses an artificial intelligence
            learning model (“AI”) to assess rope abrasion and to make recommendations based
            upon such assessment. Insight AI is a tool intended to improve the consistency and
            accuracy of rope abrasion assessment. It is recommended that Insight AI be used in
            conjunction with a line management program that includes in-person inspections
            and residual break testing to improve the safety and accuracy of rope condition
            evaluation. However, while regular use Insight AI should in general increase the
            likelihood of successful determinations, Insight AI is an enhancement tool and not a
            complete substitute for in-person inspection of ropes by trained personnel or
            determining rope strength through residual break testing.
            Certain limitations relate to AI systems generally. AI systems do not apply human
            judgment and may misinterpret data or fail to consider factors that would be
            apparent to a human. Further, AI systems depend on the data they are trained
            upon. Insufficient data, and inaccuracies in or omissions from training data, will
            affect the efficacy of recommendations made by AI and may render them invalid -
            especially in the initial period of use of the Samson Insight AI systems.
            Further, in respect of application of AI to Samson's Insight AI, visual inspection of
            rope is inherently difficult and optimally requires careful observation and judgment
            calls. Insight AI depends on the photographic and other information transmitted to
            Samson by the user. The Insight AI tool only analyzes the section of rope included
            in the photographic information submitted. Each individual section of a rope can
            experience different operational conditions which may affect the wear and strength
            loss for such sections. Also, in certain cases, multiple forms of damage in proximity
            over a short length of rope may not account for the cumulative effects on rope health
            of contiguous damage. There can be no assurances that conclusions with respect to
            the section of rope submitted to Insight AI are applicable to other sections of the
            subject rope. Further, visual inspections are ineffective as to damage types with no
            visual marker.
            When using, handling, storing and/or inspecting any Product, the user must adhere
            to the guidelines contained in Samson's Rope User Manual, as well as all industry
            or governmental guidance and regulations applicable to the application. Examples
            of industry and governmental guidance and regulations include ISO, Cordage
            Institute, ASME, OCIMF, ANSI, NFPA, and SOLAS. Use, handling. storage or
            inspection of Product inconsistent with Samson's Rope User Manual or such other
            guidance and regulations will have materially adverse and unpredictable effects on
            the health of Products.
            Samson Services do not void or negate any warnings associated with any Product.
            Samson Services, including but not limited to instruction, inspection, repair,
            maintenance, condition assessment, damage and causation analysis, as well as
            recommendations for future usage and life of the Product, do not constitute a
            guaranty of future performance.
          </p>
        </ion-content>
      </ng-template>
    </ion-modal>
  </div>
  }
</ion-content>