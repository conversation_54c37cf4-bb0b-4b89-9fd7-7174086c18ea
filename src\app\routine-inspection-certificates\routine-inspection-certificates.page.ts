import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, NotificationListenerType, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, NavController, ModalController, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { AppConstant } from 'src/constants/appConstants';
import { LMD_HEADER } from 'src/models/LMD_HEADER';
import { GenericListPage } from '../generic-list/generic-list.page';
import { AlertService } from '../services/alert.service';
import { CameraService } from '../services/camera.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { LmdService } from '../services/lmd.service';
import { PresentToastService } from '../services/present-toast.service';
import { UserPreferenceService } from '../services/user-preference.service';
import { UtilserviceService } from '../services/utilservice.service';
import { VisionAIService } from '../services/vision-ai.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { InsightAIRopeDetectionPage } from '../Insight-AI/insight-ai-rope-detection/insight-ai-rope-detection.page';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip, faTimes, faCircleInfo, faList, faTape, faCircleCheck, faCircleExclamation, faFloppyDisk, faArrowRight } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-routine-inspection-certificates',
  templateUrl: './routine-inspection-certificates.page.html',
  styleUrls: ['./routine-inspection-certificates.page.scss'],
})
export class RoutineInspectionCertificatesPage implements OnInit {
  mainlines: any[] = [];
  selectedAsset: any;
  selectedAssetId: any;
  selectedAssetName: any;
  selectedAccount: any;
  selectedIndustry:string ='';
  selectedMainline: any = [];
  selectedCerts: any = [];
  tempArray: any = [];
  tempCertArray: any = []
  rpsList: any;
  selectedCert: any;
  selectedCertNum: any;
  selectedCertNo: any;
  selectedMainlineList: any = [];
  selectedCertList: any = [];
  selectedAssetActivity: any;
  isEditLmd: boolean = false;
  isFromConfig: boolean = false;
  readOnly: boolean = false;
  footerClose: boolean = true;
  isFromInProgress: boolean = false;
  tailLength: any;
  selectedTail: any;
  fieldSegment: string = "mains"
  syncButtonClicked: boolean = false;
  // showDiv: boolean = false;
  showWorkboatSettings: boolean = false;
  isDivOpen: boolean = false;

  smallestVisibleDiamValue: any;
  smallestVisibleDiamErrorMessage: any;
  largestVisibleDiamValue: any;
  largestVisibleDiamErrorMessage: any;
  cutStrandCountCoverValue: any;
  cutStrandCountCoverErrorMessage: any;
  smallestVisibleDiamZoneTwoValue: any;
  smallestVisibleDiamZoneTwoErrorMessage: any;
  largestVisibleDiamZoneTwoValue: any;
  largestVisibleDiamZoneTwoErrorMessage: any;
  cutStrandCountCoverZoneTwoValue: any;
  cutStrandCountCoverZoneTwoErrorMessage: any;

  eventDateForm: FormGroup;
  smallestVisibleDiam: FormGroup;
  largestVisibleDiam: FormGroup;
  cutStrandCountCover: FormGroup;
  smallestVisibleDiamZoneTwo: FormGroup;
  largestVisibleDiamZoneTwo: FormGroup;
  cutStrandCountCoverZoneTwo: FormGroup;
  numberOfJacketRuptures: FormGroup;
  numberOfJacketsRepair: FormGroup;
  totalOfOperations: FormGroup;
  totalWorkingHours: FormGroup;
  anomoliesSelected = [];
  anomalyList = [];
  partitalMainsSelected:boolean=false;
  allMainlinesSelectedFlag:boolean=false;
  allMainsSelected:boolean=false;

  partitalTailsSelected:boolean=false;
  allTailsSelectedFlag:boolean=false;
  allTailsSelected:boolean=false;
  itemChecked:boolean=false;
  mainlineSelected:number;
  tailSelected:number;
  // cropArea: any;
  imgData: string;
  tiledImagesArr: any[] = [];
  isUtility: boolean= false;
  constructor(public helpService: HelpService,
    public unviredSDK: UnviredCordovaSDK,
    public menu: MenuController,
    public utilityService: UtilserviceService,
    public lmdService: LmdService,
    public navCtrl: NavController,
    public alertService: AlertService,
    public modalController: ModalController,
    public dataService: DataService,
    public translate: TranslateService,
    public alertController: AlertController,
    public device: Device,
    public userPreferenceService: UserPreferenceService,
    private cdRef: ChangeDetectorRef,
    public formBuilder: FormBuilder,
    public router: Router,
    private toastService:PresentToastService,
    private cameraService:CameraService,
    private visionAIService:VisionAIService, 
    public faIconLibrary : FaIconLibrary) {

      this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip, faCircleCheck, faCircleInfo, faCircleExclamation, faTimes, faArrowRight, faCircleExclamation, faFloppyDisk)
   

    this.eventDateForm = this.formBuilder.group({
      eventDateCtrl: [''],
    });
  }

  onFocusUserInputField(ev: any, inputValue: any) {
    inputValue = this.helpService.moveTextAreaCursorToEndForWindows(ev);
  }

  async ngOnInit() {
    this.smallestVisibleDiam = this.formBuilder.group({
      smallestVisibleDiamCtrl: [''],
      helpSmallestVisibleDiamCtrl: [{ value: '', disabled: true }, Validators.required]
    });
    this.largestVisibleDiam = this.formBuilder.group({
      largestVisibleDiamCtrl: [''],
      helpLargestVisibleDiamCtrl: [{ value: '', disabled: true }, Validators.required]
    });
    this.cutStrandCountCover = this.formBuilder.group({
      cutStrandCountCoverCtrl: [''],
      helpCutStrandCountCoverCtrl: [{ value: '', disabled: true }, Validators.required]
    });

    this.smallestVisibleDiamZoneTwo = this.formBuilder.group({
      smallestVisibleDiamZoneTwoCtrl: [''],
      helpSmallestVisibleDiamZoneTwoCtrl: [{ value: '', disabled: true }, Validators.required]
    });
    this.largestVisibleDiamZoneTwo = this.formBuilder.group({
      largestVisibleDiamZoneTwoCtrl: [''],
      helpLargestVisibleDiamZoneTwoCtrl: [{ value: '', disabled: true }, Validators.required]
    });
    this.cutStrandCountCoverZoneTwo = this.formBuilder.group({
      cutStrandCountCoverZoneTwoCtrl: [''],
      helpCutStrandCountCoverZoneTwoCtrl: [{ value: '', disabled: true }, Validators.required]
    });

    this.totalWorkingHours = this.formBuilder.group({
      totalWorkingHoursCtrl: [''],
      helpTotalWorkingHoursCtrl: [{ value: '', disabled: true }, Validators.required]
    });

    this.totalOfOperations = this.formBuilder.group({
      totalOfOperationsCtrl: [''],
      helpTotalOfOperationsCtrl: [{ value: '', disabled: true }, Validators.required]
    });

    this.numberOfJacketRuptures = this.formBuilder.group({
      numberOfJacketRupturesCtrl: [''],
      helpNumberOfJacketRupturesCtrl: [{ value: '', disabled: true }, Validators.required]
    });

    this.numberOfJacketsRepair = this.formBuilder.group({
      numberOfJacketsRepairCtrl: [''],
      helpNumberOfJacketsRepairCtrl: [{ value: '', disabled: true }, Validators.required]
    });

    await this.getAnomalyList();

    this.unviredSDK.registerNotifListener().subscribe((result) => {
      this.unviredSDK.logInfo("HOME", "registerNotifListener", JSON.stringify(result));
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.dataChanged:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.dataSend:
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.appReset:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          // this.syncButtonClicked = false;
          // this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.infoMessage:
          this.dataService.clearDataRefresh();
          this.handleInfoMessage(result);
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.serverError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        // default:
        //   this.syncButtonClicked = false;
        //   this.dataService.clearDataRefresh();
        //   this.refreshData();
        //   break;
      }
    });
    window.addEventListener('keyboardDidHide', () => {
      this.footerClose = true;
    });
    window.addEventListener('keyboardWillShow', (event) => {
      this.footerClose = false;
    });
    console.log("testinf data")
    // this.selectedAsset = this.lmdService.getSelectedAsset()
    var selectedPrevoiusAccpount = await this.userPreferenceService.getUserPreference('account');
    if (selectedPrevoiusAccpount != '' && selectedPrevoiusAccpount != undefined && selectedPrevoiusAccpount != null && selectedPrevoiusAccpount != 'null') {
      this.selectedAccount = JSON.parse(selectedPrevoiusAccpount)
    }
    var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    if (selectedAsset != undefined && selectedAsset != null && selectedAsset != '' && selectedAsset != '""') {
      this.selectedAsset = JSON.parse(selectedAsset)
    }
    if (this.selectedAsset == undefined || this.selectedAccount == undefined) {
      this.showAlert("Warning", "Please set asset and account in preference for viewing the data.")
      return
    }
    var tempObject = new LMD_HEADER();
    tempObject.LMD_ID = UtilserviceService.guid();
    tempObject.LMD_TYPE = 'RoutineInspection'
    tempObject.LMD_STATUS = "InProgress"
    tempObject.LMD_NAME = 'RoutineInspection'
    tempObject.LMD_DATA = '{}'
    this.selectedAssetActivity = tempObject;
    this.selectedAssetId = this.selectedAsset.ID
    this.isEditLmd = this.utilityService.getLMDEditMode()
    this.readOnly = this.lmdService.getReadOnlyMode();
    this.isFromInProgress = this.lmdService.getIsFromInProgress()
    this.isFromConfig = this.lmdService.getFromConfig()
    this.getWinches()

    if (this.fieldSegment == '') {
      this.fieldSegment = "mains"
    }
    await this.checkIfWorkboatOrUtility();
    // & check if the user is enabled to insight AI
    await this.checkInsightAIEnabledForUser();
  }

  async checkInsightAIEnabledForUser() {
    let insightAIEnabled = await this.dataService.userEnabledForInsightAI();
  }

  hasErrorStart(selectedField, val) {
    // switch (selectedField) {
    //   case 'smallestVisibleDiam':
    //     if (parseFloat(this.smallestVisibleDiamValue) > 7) {
    //       this.smallestVisibleDiam.controls.smallestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamErrorMessage = "Start value cannot be greater than inspected length 7 "
    //       return true;
    //     } else if (parseFloat(this.smallestVisibleDiamValue) < 0) {
    //       this.smallestVisibleDiam.controls.smallestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamErrorMessage = "Start value cannot be less than 0"
    //       return true;
    //     } else if ((this.smallestVisibleDiamValue == '' && this.smallestVisibleDiamValue != 0) || this.smallestVisibleDiamValue == null || this.smallestVisibleDiamValue == undefined) {
    //       this.smallestVisibleDiam.controls.smallestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamErrorMessage = "Start value is mandatory"
    //       return true;
    //     } else if (isNaN(parseFloat(this.smallestVisibleDiamValue))) {
    //       this.smallestVisibleDiam.controls.smallestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamErrorMessage = "Invalid start value"
    //       return true;
    //     } else {
    //       return false;
    //     }
    //     break;
    //   case 'largestVisibleDiam':
    //     if (parseFloat(this.largestVisibleDiamValue) > 7) {
    //       this.largestVisibleDiam.controls.largestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamErrorMessage = "Start value cannot be greater than inspected length 7 "
    //       return true;
    //     } else if (parseFloat(this.largestVisibleDiamValue) < 0) {
    //       this.largestVisibleDiam.controls.largestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamErrorMessage = "Start value cannot be less than 0"
    //       return true;
    //     } else if ((this.largestVisibleDiamValue == '' && this.largestVisibleDiamValue != 0) || this.largestVisibleDiamValue == null || this.largestVisibleDiamValue == undefined) {
    //       this.largestVisibleDiam.controls.largestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamErrorMessage = "Start value is mandatory"
    //       return true;
    //     } else if (isNaN(parseFloat(this.largestVisibleDiamValue))) {
    //       this.largestVisibleDiam.controls.largestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamErrorMessage = "Invalid start value"
    //       return true;
    //     } else {
    //       return false;
    //     }
    //     break;
    //   case 'cutStrandCountCover':
    //     if (parseFloat(this.cutStrandCountCoverValue) > 7) {
    //       this.cutStrandCountCover.controls.cutStrandCountCoverCtrl.setErrors({ 'incorrect': true })
    //       this.cutStrandCountCoverErrorMessage = "Start value cannot be greater than inspected length 7 "
    //       return true;
    //     } else if (parseFloat(this.cutStrandCountCoverValue) < 0) {
    //       this.cutStrandCountCover.controls.cutStrandCountCoverCtrl.setErrors({ 'incorrect': true })
    //       this.cutStrandCountCoverErrorMessage = "Start value cannot be less than 0"
    //       return true;
    //     } else if ((this.cutStrandCountCoverValue == '' && this.cutStrandCountCoverValue != 0) || this.cutStrandCountCoverValue == null || this.cutStrandCountCoverValue == undefined) {
    //       this.cutStrandCountCover.controls.cutStrandCountCoverCtrl.setErrors({ 'incorrect': true })
    //       this.cutStrandCountCoverErrorMessage = "Start value is mandatory"
    //       return true;
    //     } else if (isNaN(parseFloat(this.cutStrandCountCoverValue))) {
    //       this.cutStrandCountCover.controls.cutStrandCountCoverCtrl.setErrors({ 'incorrect': true })
    //       this.cutStrandCountCoverErrorMessage = "Invalid start value"
    //       return true;
    //     } else {
    //       return false;
    //     }
    //     break;
    //   case 'smallestVisibleDiamZoneTwo':
    //     if (parseFloat(this.smallestVisibleDiamZoneTwoValue) > 7) {
    //       this.smallestVisibleDiamZoneTwo.controls.smallestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamZoneTwoErrorMessage = "Start value cannot be greater than inspected length 7 "
    //       return true;
    //     } else if (parseFloat(this.smallestVisibleDiamZoneTwoValue) < 0) {
    //       this.smallestVisibleDiamZoneTwo.controls.smallestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamZoneTwoErrorMessage = "Start value cannot be less than 0"
    //       return true;
    //     } else if ((this.smallestVisibleDiamZoneTwoValue == '' && this.smallestVisibleDiamZoneTwoValue != 0) || this.smallestVisibleDiamZoneTwoValue == null || this.smallestVisibleDiamZoneTwoValue == undefined) {
    //       this.smallestVisibleDiamZoneTwo.controls.smallestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamZoneTwoErrorMessage = "Start value is mandatory"
    //       return true;
    //     } else if (isNaN(parseFloat(this.smallestVisibleDiamZoneTwoValue))) {
    //       this.smallestVisibleDiamZoneTwo.controls.smallestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamZoneTwoErrorMessage = "Invalid start value"
    //       return true;
    //     } else {
    //       return false;
    //     }
    //     break;
    //   case 'largestVisibleDiamZoneTwo':
    //     if (parseFloat(this.largestVisibleDiamZoneTwoValue) > 7) {
    //       this.largestVisibleDiamZoneTwo.controls.largestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamZoneTwoErrorMessage = "Start value cannot be greater than inspected length 7 "
    //       return true;
    //     } else if (parseFloat(this.largestVisibleDiamZoneTwoValue) < 0) {
    //       this.largestVisibleDiamZoneTwo.controls.largestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamZoneTwoErrorMessage = "Start value cannot be less than 0"
    //       return true;
    //     } else if ((this.largestVisibleDiamZoneTwoValue == '' && this.largestVisibleDiamZoneTwoValue != 0) || this.largestVisibleDiamZoneTwoValue == null || this.largestVisibleDiamZoneTwoValue == undefined) {
    //       this.largestVisibleDiamZoneTwo.controls.largestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamZoneTwoErrorMessage = "Start value is mandatory"
    //       return true;
    //     } else if (isNaN(parseFloat(this.largestVisibleDiamZoneTwoValue))) {
    //       this.largestVisibleDiamZoneTwo.controls.largestVisibleDiamZoneTwoCtrl.setErrors({ 'incorrect': true })
    //       this.largestVisibleDiamZoneTwoErrorMessage = "Invalid start value"
    //       return true;
    //     } else {
    //       return false;
    //     }
    //     break;
    //   case 'cutStrandCountCoverZoneTwo':
    //       if (parseFloat(this.cutStrandCountCoverZoneTwoValue) > 7) {
    //         this.cutStrandCountCoverZoneTwo.controls.cutStrandCountCoverCtrl.setErrors({ 'incorrect': true })
    //         this.cutStrandCountCoverZoneTwoErrorMessage = "Start value cannot be greater than inspected length 7 "
    //         return true;
    //       } else if (parseFloat(this.cutStrandCountCoverZoneTwoValue) < 0) {
    //         this.cutStrandCountCoverZoneTwo.controls.cutStrandCountCoverZoneTwoCtrl.setErrors({ 'incorrect': true })
    //         this.cutStrandCountCoverZoneTwoErrorMessage = "Start value cannot be less than 0"
    //         return true;
    //       } else if ((this.cutStrandCountCoverZoneTwoValue == '' && this.cutStrandCountCoverZoneTwoValue != 0) || this.cutStrandCountCoverZoneTwoValue == null || this.cutStrandCountCoverZoneTwoValue == undefined) {
    //         this.cutStrandCountCoverZoneTwo.controls.cutStrandCountCoverZoneTwoCtrl.setErrors({ 'incorrect': true })
    //         this.cutStrandCountCoverZoneTwoErrorMessage = "Start value is mandatory"
    //         return true;
    //       } else if (isNaN(parseFloat(this.cutStrandCountCoverZoneTwoValue))) {
    //         this.cutStrandCountCoverZoneTwo.controls.cutStrandCountCoverZoneTwoCtrl.setErrors({ 'incorrect': true })
    //         this.cutStrandCountCoverZoneTwoErrorMessage = "Invalid start value"
    //         return true;
    //       } else {
    //         return false;
    //       }
    //       break;

    //     case 'numberOfJacketsRepair':
    //     if (parseFloat(val) < 0) {
    //       this.numberOfJacketsRepair.controls.numberOfJacketsRepairCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamErrorMessage = "Jaccket repair value cannot be less than 0"
    //       return true;
    //     } else if ((this.smallestVisibleDiamValue == '' && this.smallestVisibleDiamValue != 0) || this.smallestVisibleDiamValue == null || this.smallestVisibleDiamValue == undefined) {
    //       this.smallestVisibleDiam.controls.smallestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamErrorMessage = "Jaccket repair value is mandatory"
    //       return true;
    //     } else if (isNaN(parseFloat(this.smallestVisibleDiamValue))) {
    //       this.smallestVisibleDiam.controls.smallestVisibleDiamCtrl.setErrors({ 'incorrect': true })
    //       this.smallestVisibleDiamErrorMessage = "Invalid start value"
    //       return true;
    //     } else {
    //       return false;
    //     }
    //     break;

    // }


  }

  async checkIfWorkboatOrUtility() {
    var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
    this.selectedIndustry = JSON.parse(selectedIndustry).NAME;
    if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
      selectedIndustry = JSON.parse(selectedIndustry)
      this.isUtility = selectedIndustry.ID.includes('Utility');
      if (!selectedIndustry.ID.includes("Workboat")) {
        return
      }
      this.fieldSegment = "tails"
      this.showWorkboatSettings = true;
      return
    } else {
      return
    }
  }

  ngAfterContentChecked() {
    // console.log("test ngAfterContentChecked")
    this.cdRef.detectChanges();
  }


  public handleInfoMessage(result) {

    this.unviredSDK.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });

    this.showAlert(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }

  async showAlert(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  addWinches() {

  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  async getWinches() {
    // let equipmentRes = await this.unviredSDK.dbExecuteStatement(`SELECT CERTIFICATE_HEADER.NAME, CERTIFICATE_HEADER.CERTIFICATE_NUM, CERTIFICATE_HEADER.PRODUCT, CERTIFICATE_HEADER.DIAM, CERTIFICATE_HEADER.RPS, ROPE_PRODUCT_SPEC_HEADER.END_IN_USE, ROPE_PRODUCT_SPEC_HEADER.CURRENT_LENGTH_IN_METER FROM CERTIFICATE_HEADER, ROPE_PRODUCT_SPEC_HEADER WHERE CERTIFICATE_HEADER.ASSET_ID = '${this.selectedAssetId}' AND CERTIFICATE_HEADER.ACCOUNT_ID = '${this.selectedAccount.ID}' AND CERTIFICATE_HEADER.RPS = ROPE_PRODUCT_SPEC_HEADER.ID AND ( ROPE_PRODUCT_SPEC_HEADER.APPLICATION not like 'Mooring Tails'  OR  ROPE_PRODUCT_SPEC_HEADER.APPLICATION is NULL) AND ( ROPE_PRODUCT_SPEC_HEADER.APPLICATION not like 'Mooring Tails'  OR  ROPE_PRODUCT_SPEC_HEADER.APPLICATION is NULL) AND (CERTIFICATE_HEADER.CONSTRUCTION like 'HMPE' OR CERTIFICATE_HEADER.CONSTRUCTION like 'Jacketed' OR CERTIFICATE_HEADER.CONSTRUCTION like 'Wire')`)
    let query = `SELECT A.* FROM ( SELECT CERTIFICATE_HEADER.NAME,
                      CERTIFICATE_HEADER.CERTIFICATE_NUM,
                      CERTIFICATE_HEADER.PRODUCT,
                      CERTIFICATE_HEADER.DIAM,
                      CERTIFICATE_HEADER.RPS,
                      CERTIFICATE_HEADER.LOCATION_INSTALLED,
                      CERTIFICATE_HEADER.CONSTRUCTION,
                      ROPE_PRODUCT_SPEC_HEADER.END_IN_USE,
                      ROPE_PRODUCT_SPEC_HEADER.CURRENT_LENGTH_IN_METER,
                      ROPE_PRODUCT_SPEC_HEADER.APPLICATION,
                      ROPE_PRODUCT_SPEC_HEADER.PRODUCT_TYPE,
                      ROPE_PRODUCT_SPEC_HEADER.WORKING_HRS,
                      ROPE_PRODUCT_SPEC_HEADER.WORKING_OPERATIONS,
                      ROPE_PRODUCT_SPEC_HEADER.MFG,
                      ROPE_PRODUCT_SPEC_HEADER.PRODUCT_NAME,
                      ROPE_PRODUCT_SPEC_HEADER.RECORD_TYPE,
                      ROPE_PRODUCT_SPEC_HEADER.NAME As RPS_NAME,
                      ROPE_PRODUCT_SPEC_HEADER.EQUIP_DETAILS
                FROM  CERTIFICATE_HEADER,
                      ROPE_PRODUCT_SPEC_HEADER
                WHERE CERTIFICATE_HEADER.ASSET_ID = '${this.selectedAssetId}'
                      AND CERTIFICATE_HEADER.ACCOUNT_ID = '${this.selectedAccount.ID}'
                      AND CERTIFICATE_HEADER.RPS = ROPE_PRODUCT_SPEC_HEADER.ID
                      AND (ROPE_PRODUCT_SPEC_HEADER.APPLICATION like 'Mainline')) AS A`;
    let equipmentRes = await this.unviredSDK.dbExecuteStatement(query)
    if (equipmentRes.type == ResultType.success) {
      if (equipmentRes.data.length > 0) {
        this.mainlines = equipmentRes.data;
        for (var i = 0; i < this.mainlines.length; i++) {
          this.mainlines[i].CHECKED_ITEM = false;
          if(this.mainlines[i].RECORD_TYPE != 'Competitor Product') {
            if (this.mainlines[i].CONSTRUCTION && this.mainlines[i].CONSTRUCTION != '') {
              if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('12-Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('12 Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8 Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8-Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('16 Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('16-Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('3 Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('3-Strand').toLowerCase()) > -1) {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
              } else if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8 strand Jacketed').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Core Dependent').toLowerCase())) {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
              } else if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Steel Wire').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Wire').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Steel').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6XSES(37)+IWRC').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6x36 IWRC').toLowerCase()) > -1) {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
              } else if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6-Strand').toLowerCase()) > -1 ||
                this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6 Strand').toLowerCase()) > -1) {
                if (this.mainlines[i].PRODUCT_NAME && this.mainlines[i].PRODUCT_NAME != '') {
                  if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton 8').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton-8').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel  Blue').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('AS-78').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-X').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel X').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Unknown').toLowerCase()) > -1) {
                    this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                  } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO-75').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO 75').toLowerCase()) > -1) {
                    this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
                  } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Steel Wire Rope').toLowerCase()) > -1 ||
                    this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Delta Filler Rope').toLowerCase()) > -1) {
                    this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
                  } else {
                    this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                  }
                } else {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                }
              } else if (this.mainlines[i].PRODUCT_NAME && this.mainlines[i].PRODUCT_NAME != '') {
                if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton 8').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton-8').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel  Blue').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('AS-78').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-X').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel X').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Unknown').toLowerCase()) > -1) {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO-75').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO 75').toLowerCase()) > -1) {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
                } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Steel Wire Rope').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Delta Filler Rope').toLowerCase()) > -1) {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
                } else if (this.mainlines[i].MFG && this.mainlines[i].MFG != '') {
                  if (this.mainlines[i].MFG == 'Samson') {
                    if (this.mainlines[i].MATERIAL && this.mainlines[i].MATERIAL != '') {
                      if (this.mainlines[i].MATERIAL.toLowerCase().indexOf(('HMPE').toLowerCase()) > -1) {
                        this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                      } else {
                        this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
                      }
                    } else {
                      this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                    }
                  } else {
                    this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                  }
                } else {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
                }
              } else {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
              }
            } else {
              this.mainlines[i].CUSTOM_CONSTRUCTION = ''
            }
          } else if(this.mainlines[i].RECORD_TYPE == 'Competitor Product') {
            if(this.mainlines[i].PRODUCT_TYPE!=null && this.mainlines[i].PRODUCT_TYPE.toLowerCase().indexOf(('HMSF (Class II)').toLowerCase()) > -1) {
              this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
            } else if(this.mainlines[i].PRODUCT_TYPE!=null && this.mainlines[i].PRODUCT_TYPE.toLowerCase().indexOf(('Steel').toLowerCase()) > -1) {
              this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
            } else {
              this.mainlines[i].CUSTOM_CONSTRUCTION = ""
            }
          } else if (this.mainlines[i].CONSTRUCTION && this.mainlines[i].CONSTRUCTION != '') {
            if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('12-Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('12 Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8 Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8-Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('16 Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('16-Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('3 Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('3-Strand').toLowerCase()) > -1 ) {
              this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
            } else if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8 strand Jacketed').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Core Dependent').toLowerCase()) > -1
            ) {
              this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
            } else if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Steel Wire').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Wire').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('Steel').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6XSES(37)+IWRC').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6x36 IWRC').toLowerCase()) > -1) {
              this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
            } else if (this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6-Strand').toLowerCase()) > -1 ||
              this.mainlines[i].CONSTRUCTION.toLowerCase().indexOf(('6 Strand').toLowerCase()) > -1) {
              if (this.mainlines[i].PRODUCT_NAME && this.mainlines[i].PRODUCT_NAME != '') {
                if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton 8').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton-8').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel  Blue').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('AS-78').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-X').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel X').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Unknown').toLowerCase()) > -1) {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO-75').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO 75').toLowerCase()) > -1) {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
                } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Steel Wire Rope').toLowerCase()) > -1 ||
                  this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Delta Filler Rope').toLowerCase()) > -1) {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
                } else {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                }
              } else {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
              }
            } else if (this.mainlines[i].PRODUCT_NAME && this.mainlines[i].PRODUCT_NAME != '') {
              if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton 8').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Proton-8').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel  Blue').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('AS-78').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel-X').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Amsteel X').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Unknown').toLowerCase()) > -1) {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
              } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO-75').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('TURBO 75').toLowerCase()) > -1) {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
              } else if (this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Steel Wire Rope').toLowerCase()) > -1 ||
                this.mainlines[i].PRODUCT_NAME.toLowerCase().indexOf(('Delta Filler Rope').toLowerCase()) > -1) {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
              } else if (this.mainlines[i].MFG && this.mainlines[i].MFG != '') {
                if (this.mainlines[i].MFG == 'Samson') {
                  if (this.mainlines[i].MATERIAL && this.mainlines[i].MATERIAL != '') {
                    if (this.mainlines[i].MATERIAL.toLowerCase().indexOf(('HMPE').toLowerCase()) > -1) {
                      this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                    } else {
                      this.mainlines[i].CUSTOM_CONSTRUCTION = 'Jacketed'
                    }
                  } else {
                    this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                  }
                } else {
                  this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
                }
              } else {
                this.mainlines[i].CUSTOM_CONSTRUCTION = 'Wire'
              }
            } else {
              this.mainlines[i].CUSTOM_CONSTRUCTION = 'HMPE'
            }
          } else {
            this.mainlines[i].CUSTOM_CONSTRUCTION = ''
          }
        }
      } else {
        this.mainlines = []
      }
    } else {
      this.unviredSDK.logError("create-inspection", "filterCertificate", "Error while getting error from db" + JSON.stringify(this.mainlines))
    }
    if (!this.isEditLmd && this.isFromConfig) {
      var tempLmdData = JSON.parse(this.selectedAssetActivity.LMD_DATA)
      if (tempLmdData.winches != undefined) {
        this.fieldSegment = tempLmdData.winchType
        if (this.fieldSegment == 'tails') {
          this.selectedCertList = tempLmdData.winches
          for (var i = 0; i < this.selectedCertList.length; i++) {
            var pushItem = true
            for (var x = 0; x < this.selectedCerts.length; x++) {
              if (this.selectedCerts[x] == this.selectedCertList[i].CERTIFICATE_NUM) {
                pushItem = false;
                break;
              }
            }
            if (pushItem == true) {
              this.selectedCerts.push(this.selectedCertList[i].CERTIFICATE_NUM)
            } else {
              this.selectedCertList.splice(i, 1)
            }
          }
        } else {
          if (this.fieldSegment == '') {
            this.fieldSegment = 'equipment'
          }
          this.selectedMainlineList = tempLmdData.winches
          for (var i = 0; i < this.selectedMainlineList.length; i++) {
            this.selectedMainline.push(this.selectedMainlineList[i].CERTIFICATE_NUM)
          }
        }
      }
    }
    console.log(this.mainlines);
    const groupedData = this.mainlines.reduce((acc, item) => {
      (acc[item.CUSTOM_CONSTRUCTION] = acc[item.CUSTOM_CONSTRUCTION] || []).push(item);
      return acc;
    }, {});
    
    // Print each group
    for (const CUSTOM_CONSTRUCTION in groupedData) {
      console.log(`Group ${CUSTOM_CONSTRUCTION}:`);
      console.log(groupedData[CUSTOM_CONSTRUCTION]);
    }
    this.getRPSDetails()
  }

  async onCheckedMainline(ev:any,event: any, i:number, certObj?:any) {
    console.log(certObj);
    this.mainlineSelected=i;
    let index = this.selectedMainline.indexOf(event);
    if (index > -1) {
      this.selectedMainline.splice(index, 1);
      this.selectedMainlineList.splice(index, 1)
      let ind=this.tempArray.indexOf(i,0)
      if(ind > -1) {
        this.tempArray.splice(ind, 1);
      }
      if(this.tempArray.length==0) {
        this.isDivOpen = false;
      }
      if(this.selectedMainline.length==0) {
        this.allMainlinesSelectedFlag=false;
        this.partitalMainsSelected = false;
        this.allMainsSelected=false;
      } else {
        this.partitalMainsSelected = true;
      }
    } else {
      if(this.mainlines[i].RECORD_TYPE == 'Competitor Product' && (this.mainlines[i].PRODUCT_TYPE==null || (this.mainlines[i].PRODUCT_TYPE!='HMSF (Class II)' && this.mainlines[i].PRODUCT_TYPE!='Steel'))) {
        if((<HTMLInputElement>ev.target).checked==true) {
          (<HTMLInputElement>ev.target).checked=false;
          if(this.toastService.isToastPresent) {
            await this.toastService.dismiss();
          }
          if(this.itemChecked==true) {
            await this.toastService.present("bottom",this.translate.instant("routine_inspection_incomplete_data_error_msg"));
            this.itemChecked=false;
          }
        }
      } else {
        var temp = {
          "CERTIFICATE_NUM": event,
          "CERT_NAME": this.mainlines[i].NAME,
          "INSPECTION_DATE": '',
          "END_IN_USE": this.mainlines[i].END_IN_USE,
          "WINCH_ID": this.mainlines[i].EQUIP_DETAILS,
          "WINCH_NAME": this.mainlines[i].EQUIP_NAME,
          "ADDITIONAL_NUMBER_OF_JACKETS_REPAIR": '',
          "ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES": '',
          "EXTRA_PASS_FAIL": '',
          "EXTRA_TOTAL_OF_OPERATION": this.mainlines[i].WORKING_OPERATIONS,
          "EXTRA_TOTAL_WORKING_HOURS": this.mainlines[i].WORKING_HRS,
          "ZONE_TWO_SMALLEST_VISIBLE_DIAM": '',
          "ZONE_TWO_LARGEST_VISIBLE_DIAM": '',
          "ZONE_TWO_CUT_STRAND_COUNT_COVER": '',
          "ZONE_ONE_SMALLEST_VISIBLE_DIAM": '',
          "ZONE_ONE_LARGEST_VISIBLE_DIAM": '',
          "ZONE_ONE_CUT_STRAND_COUNT_COVER": '',
          "ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION": '',
          "ZONE_ONE_WAVINESS_GAP_MEASUREMENT": '',
          "ZONE_ONE_MAX_WIRE_BREAKES": '',
          "ZONE_TWO_WAVINESS_GAP_MEASUREMENT": '',
          "ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION": '',
          "ANOMOLIES_SELECTED": [],
          "ANOMOLIES_VALLEY_WIRE_BREAKS_IN_6_DIAM": '',
          "ANOMOLIES_WIRE_BREAKS_IN_30_DIAM": '',
          "ANOMOLIES_EXTERNAL_COROSION": '',
          "ZONE_ONE_EXTERNAL_ABRASION": '',
          "ZONE_ONE_INTERNAL_ABRASION": '',
          "ZONE_ONE_CUT_YARN_COUNT": '',
          "ZONE_ONE_LENGTH_OF_GLAZING": '',
          "ZONE_TWO_EXTERNAL_ABRASION": '',
          "ZONE_TWO_INTERNAL_ABRASION": '',
          "ZONE_TWO_CUT_YARN_COUNT": '',
          "ZONE_TWO_LENGTH_OF_GLAZING": '',
          "TWIST_PER_METER": '',
          "CHAFE_GEAR_HOLE_COUNT": '',
          "TAIL_BEARING_POINT_CONNECTION": '',
          "TAIL_FULLY_CUT_STRANDS": '',
          "NOTES": "",
          "showJacketedError": false,
          "showTailError": false,
          "showWireError": false,
          "showHmpeError": false,
          "showExternalErrorMessage" : false,
          "showExternalErrorMessageType" : '',
          "ASSET": this.selectedAssetId,
          "ASSET_NAME": this.selectedAsset.NAME,
          "CONSTRUCTION_TYPE": this.mainlines[i].CONSTRUCTION,
          "CUSTOM_CONSTRUCTION": this.mainlines[i].CUSTOM_CONSTRUCTION,
          "APPLICATION": this.mainlines[i].APPLICATION,
          "DIAM": this.mainlines[i].DIAM,
          "RPS": this.mainlines[i].RPS,
          // "SHIP_SIDE": this.mainlines[i].SHIP_SIDE,
          "INSTALLED_LOCATION": this.mainlines[i].LOCATION_INSTALLED,
          "RPS_NAME": this.mainlines[i].RPS_NAME,
          "PRODUCT": this.mainlines[i].PRODUCT,
          "DIAM_UOM": this.dataService.selectedRoutineInspectionDiamUomString,
          "ZONE_ONE_INSPECTION_TYPE" : this.dataService.isUserEnabledForInsightAI ? 'VisionAI' : 'Manual',
          "ZONE_ONE_INSIGHT_AI": false,
          "ZONE_ONE_INSIGHT_AI_IMAGE" : '',
          "ZONE_ONE_TILED_IMAGES" : [],
          "ZONE_ONE_INSIGHT_AI_IMAGE_PATH" : "",
          "ZONE_TWO_INSPECTION_TYPE" : this.dataService.isUserEnabledForInsightAI ? 'VisionAI' : 'Manual',
          "ZONE_TWO_INSIGHT_AI": false,
          "ZONE_TWO_INSIGHT_AI_IMAGE" : '',
          "ZONE_TWO_TILED_IMAGES" : [], 
          "ZONE_TWO_INSIGHT_AI_IMAGE_PATH" : ""
        }
        this.itemChecked=false;
        this.selectedMainlineList.push(temp);
        this.selectedMainline.push(event);
        if(this.selectedMainline.length == this.mainlines.filter(ele=> (ele.RECORD_TYPE !="Competitor Product") || (ele.RECORD_TYPE == "Competitor Product" && ( ele.PRODUCT_TYPE!=null && ele.PRODUCT_TYPE=='HMSF (Class II)' || ele.PRODUCT_TYPE=='Steel'))).length) {
          this.allMainsSelected = true;
          this.partitalMainsSelected = false;
          this.allMainlinesSelectedFlag = false;
        } else {
          this.partitalMainsSelected = true;
          this.allMainlinesSelectedFlag = false; 
        }
      }
    }
    // console.log(this.selectedMainline);
  }

  async onCheckedCert(event: any, i) {
    this.tailSelected=i;
    const index = this.selectedCerts.indexOf(event);
    if (index > -1) {
      console.log(index);
      this.selectedCerts.splice(index, 1);
      this.selectedCertList.splice(index, 1)
      let ind=this.tempCertArray.indexOf(i,0);
      if(ind > -1) {
        this.tempCertArray.splice(ind, 1);
      }
      if(this.tempCertArray.length==0) {
        this.isDivOpen = false;
      }
      if(this.selectedCerts.length==0) {
        this.partitalTailsSelected = false;
        this.allTailsSelected = false;
      } else {
        this.partitalTailsSelected = true;
      }
    } else {
      // this.showDiv = true
      var temp = {
        "CERTIFICATE_NUM": event,
        "CERT_NAME": this.rpsList[i].NAME,
        "INSPECTION_DATE": '',
        "END_IN_USE": '',
        "WINCH_ID": this.rpsList[i].EQUIP_DETAILS,
        "WINCH_NAME": this.rpsList[i].EQUIP_NAME,
        "ADDITIONAL_NUMBER_OF_JACKETS_REPAIR": '',
        "ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES": '',
        "EXTRA_PASS_FAIL": '',
        "EXTRA_TOTAL_OF_OPERATION": this.rpsList[i].WORKING_OPERATIONS,
        "EXTRA_TOTAL_WORKING_HOURS": this.rpsList[i].WORKING_HRS,
        "ZONE_TWO_SMALLEST_VISIBLE_DIAM": '',
        "ZONE_TWO_LARGEST_VISIBLE_DIAM": '',
        "ZONE_TWO_CUT_STRAND_COUNT_COVER": '',
        "ZONE_ONE_SMALLEST_VISIBLE_DIAM": '',
        "ZONE_ONE_LARGEST_VISIBLE_DIAM": '',
        "ZONE_ONE_CUT_STRAND_COUNT_COVER": '',
        "ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION": '',
        "ZONE_ONE_WAVINESS_GAP_MEASUREMENT": '',
        "ZONE_ONE_MAX_WIRE_BREAKES": '',
        "ZONE_TWO_WAVINESS_GAP_MEASUREMENT": '',
        "ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION": '',
        "ANOMOLIES_SELECTED": [],
        "ANOMOLIES_VALLEY_WIRE_BREAKS_IN_6_DIAM": '',
        "ANOMOLIES_WIRE_BREAKS_IN_30_DIAM": '',
        "ANOMOLIES_EXTERNAL_COROSION": '',
        "ZONE_ONE_EXTERNAL_ABRASION": '',
        "ZONE_ONE_INTERNAL_ABRASION": '',
        "ZONE_ONE_CUT_YARN_COUNT": '',
        "ZONE_ONE_LENGTH_OF_GLAZING": '',
        "ZONE_TWO_EXTERNAL_ABRASION": '',
        "ZONE_TWO_INTERNAL_ABRASION": '',
        "ZONE_TWO_CUT_YARN_COUNT": '',
        "ZONE_TWO_LENGTH_OF_GLAZING": '',
        "TWIST_PER_METER": '',
        "CHAFE_GEAR_HOLE_COUNT": '',
        "TAIL_BEARING_POINT_CONNECTION": '',
        "TAIL_FULLY_CUT_STRANDS": '',
        "NOTES": '',
        "showJacketedError": false,
        "showTailError": false,
        "showWireError": false,
        "showHmpeError": false,
        "showExternalErrorMessage" : false,
        "showExternalErrorMessageType": '',
        "ASSET": this.selectedAssetId,
        "ASSET_NAME": this.selectedAsset.NAME,
        "CONSTRUCTION_TYPE": this.rpsList[i].CONSTRUCTION,
        "CUSTOM_CONSTRUCTION": this.rpsList[i].CUSTOM_CONSTRUCTION,
        "APPLICATION": this.rpsList[i].APPLICATION,
        "DIAM": this.rpsList[i].DIAM,
        "RPS": this.rpsList[i].RPS,
        "SHIP_SIDE": this.rpsList[i].SHIP_SIDE,
        "INSTALLED_LOCATION": this.rpsList[i].INSTALLED_LOCATION,
        "RPS_NAME": this.rpsList[i].RPS_NAME,
        "PRODUCT": this.rpsList[i].PRODUCT,
        "DIAM_UOM": this.dataService.selectedRoutineInspectionDiamUomString
      }
      this.selectedCertList.push(temp);
      this.selectedCerts.push(event);
      if(this.selectedCerts.length == this.rpsList.length) {
        this.allTailsSelected = true;
        this.partitalTailsSelected= false;
        this.allTailsSelectedFlag = false;
      } else {
        this.partitalTailsSelected = true;
        this.allTailsSelectedFlag = false;
      }
    }
  }

  checkSectionIsOpenMainline(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }

  itemClicked() {
    console.log("item clicked");
  }

  checkDivIsOpenMainline(i) {
    if (this.device.platform == 'windows' || this.device.platform == 'browser') {
      return this.selectedMainline.indexOf(this.mainlines[this.mainlines.indexOf(i)].CERTIFICATE_NUM) > -1;
    } else {
      return false;
    }
  }

  helightItemMainline(i,index) {
    if (this.device.platform == 'windows' || this.device.platform == 'browser') {
      var tempIndex = this.selectedMainline.indexOf(this.mainlines[this.mainlines.indexOf(i)].CERTIFICATE_NUM)
      if (tempIndex > -1) {
        this.mainlineSelected= index;
      }
    }
  }

  checkDivIsOpenCert(i) {
    if (this.device.platform == 'windows' || this.device.platform == 'browser') {
      return this.selectedCerts.indexOf(this.rpsList[this.rpsList.indexOf(i)].CERTIFICATE_NUM) > -1;
    } else {
      return false
    }
  }

  helightItemCert(i,index) {
    if (this.device.platform == 'windows' || this.device.platform == 'browser') {
      var tempIndex = this.selectedCerts.indexOf(this.rpsList[this.rpsList.indexOf(i)].CERTIFICATE_NUM)
      if (tempIndex > -1) {
        this.tailSelected=index;
      }
    }
  }

  closeDiv() {
    if (this.fieldSegment == 'mains') {
      this.tempArray.length = 0;
      this.tempArray = [];
      this.isDivOpen = false
    } else {
      this.tempCertArray.length = 0
      this.tempCertArray = [];
      this.isDivOpen = false;
    }
  }

  checkSectionIsOpenCert(i) {
    const ind = this.tempCertArray.indexOf(i, 0);
    return (ind > -1);
  }

  async iconMainline(index) {
    let ind = this.tempArray.indexOf(index, 0);
    this.mainlineSelected= index;
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
      if(this.tempArray.length == 0) {
        this.isDivOpen = false;
      }
    } else {
        this.isDivOpen = true;
        this.tempArray.push(index);
    }
  }

  iconCert(index) {
    const ind = this.tempCertArray.indexOf(index, 0);
    this.tailSelected=index;
    if (ind > -1) {
      this.tempCertArray.splice(ind, 1);
      if(this.tempCertArray.length == 0) {
        this.isDivOpen = false;
      }
    } else {
      this.isDivOpen = true;
      this.tempCertArray.push(index);
    }
  }

  async saveInspections() {
    var error = '';
    this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","ROUTINE_INSPECTION_CERTIFICATES - SAVEINSPECTIONS METHOD START");
    if (this.selectedMainlineList.length > 0 && this.fieldSegment == 'mains') {
      var save = true;
      if (save == true) {
        if (!this.isEditLmd) {
          var counter = 0
          if (this.selectedMainlineList.some(ele => ele.INSPECTION_DATE === "")) {
            await this.alertService.showAlert("", this.translate.instant("Please fill date for all the inspections"));
          } else {
            this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","PRESENTING LOADER");
            await this.alertService.present();
            for (var ind = 0; ind < this.selectedMainlineList.length; ind++) {
              // let zone1InsightAI = 
              delete this.selectedMainlineList[ind].ZONE_ONE_INSPECTION_TYPE; // & remove ZONE1 inspection type from object to save to server and to report
              delete this.selectedMainlineList[ind].ZONE_TWO_INSPECTION_TYPE; // & remove ZONE2 inspection type from object to save to server and to report
              delete this.selectedMainlineList[ind].ZONE_ONE_INSIGHT_AI_IMAGE; // & remove ZONE1 AI Image URL from object to avoid sending to server and appearing it in report
              delete this.selectedMainlineList[ind].ZONE_TWO_INSIGHT_AI_IMAGE; // & remove ZONE2 AI Image URL from object to avoid sending to server and appearing it in report
              let zone1TiledImages:any[];
              let zone2TiledImages:any[];
              let zone1AIImagePath:any;
              let zone2AIImagePath:any;
              let selectedRopeType=''
              if(this.selectedMainlineList[ind].PRODUCT?.includes('AMSTEEL') || this.selectedMainlineList[ind].PRODUCT?.includes('ML-12')) {
                selectedRopeType = 'Amsteel'
              } else if(this.selectedMainlineList[ind].PRODUCT?.includes('TENEX')) {
                selectedRopeType = 'Tenex';
              }
              if(this.selectedMainlineList[ind].ZONE_ONE_INSIGHT_AI) {
                zone1TiledImages = this.selectedMainlineList[ind].ZONE_ONE_TILED_IMAGES;
                zone1AIImagePath = this.selectedMainlineList[ind].ZONE_ONE_INSIGHT_AI_IMAGE_PATH;
              }
              delete this.selectedMainlineList[ind].ZONE_ONE_TILED_IMAGES;
              delete this.selectedMainlineList[ind].ZONE_ONE_INSIGHT_AI_IMAGE_PATH;
              if(this.selectedMainlineList[ind].ZONE_TWO_INSIGHT_AI) {
                zone2TiledImages = this.selectedMainlineList[ind].ZONE_TWO_TILED_IMAGES;
                zone2AIImagePath = this.selectedMainlineList[ind].ZONE_TWO_INSIGHT_AI_IMAGE_PATH;
                
              }
              delete this.selectedMainlineList[ind].ZONE_TWO_TILED_IMAGES;
              delete this.selectedMainlineList[ind].ZONE_TWO_INSIGHT_AI_IMAGE_PATH;
              var tempObject = JSON.parse(this.selectedAssetActivity.LMD_DATA)
              tempObject = this.selectedMainlineList[ind];
              tempObject.INSPECTION_DATE = typeof tempObject.INSPECTION_DATE == "object" ? moment(tempObject.INSPECTION_DATE.toISOString()).format('YYYY-MM-DD') : (typeof tempObject.INSPECTION_DATE == "string" ? moment(tempObject.INSPECTION_DATE).format('YYYY-MM-DD') : '')
              this.selectedAssetActivity.LMD_DATA = JSON.stringify(tempObject)
              this.selectedAssetActivity.LMD_ID = UtilserviceService.guid();
              this.selectedAssetActivity.EXTFLD3 = this.selectedMainlineList.length;
              let tempSelectedAssetActivityObj = JSON.parse(JSON.stringify(this.selectedAssetActivity))
              var temp = await this.unviredSDK.dbInsert("LMD_HEADER", tempSelectedAssetActivityObj, true);

              if (temp.type == ResultType.success) {
                if (!this.syncButtonClicked || counter < this.selectedMainlineList.length) {
                  this.syncButtonClicked = true;
                  var whereClause = "LMD_ID like '" + this.selectedAssetActivity.LMD_ID + "'"
                  var tempFetch = await this.unviredSDK.dbSelect(AppConstant.TABLE_LMD_HEADER, whereClause)
                  if (tempFetch.type == ResultType.success) {
                    if(this.selectedMainlineList[ind].ZONE_ONE_INSIGHT_AI && zone1TiledImages.length >0 ) {
                      this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATING ROUTNIE INSPECTION ZONE1 INSIGHT AI ATTACHMENTS START");
                      let fileName='';
                      let UUID='';
                      let type='';
                      if(this.device.platform=='browser') {
                        UUID = 'image-' + (new Date().getTime()).toString(16);
                        type = zone1AIImagePath.Image.substring(zone1AIImagePath.Image.indexOf("image/") + 6, zone1AIImagePath.Image.indexOf(';'))
                        fileName = UUID+'.'+type;
                      }
                      await this.dataService.createLMDAttachmentItem(tempFetch.data[0].LID,zone1AIImagePath,fileName,selectedRopeType);
                      this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATING ROUTNIE INSPECTION ZONE1 INSIGHT AI ATTACHMENTS COMPLETED");
                      // console.log(this.selectedMainlineList[ind].ZONE_ONE_TILED_IMAGES);
                      for(let j=0;j<zone1TiledImages.length;j++) {
                        this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATING ROUTNIE INSPECTION ZONE2 INSIGHT AI TAILED IMAGES ATTACHMENTS");
                        fileName = UUID+'-T'+(j+1)+'.'+type;
                        await this.dataService.createLMDAttachmentItem(tempFetch.data[0].LID,zone1TiledImages[j],fileName,selectedRopeType).then(res=>{
                          this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATING ROUTNIE INSPECTION ZONE2 INSIGHT AI TAILED IMAGE"+j+"ATTACHMENT");
                        }).catch(err=>{
                          this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATING ROUTNIE INSPECTION ZONE2 INSIGHT AI TAILED IMAGE"+j+"ATTACHMENT");
                        });
                      }
                    }
                    if(this.selectedMainlineList[ind].ZONE_TWO_INSIGHT_AI && zone2TiledImages.length > 0 ) {
                      this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATING ROUTNIE INSPECTION ZONE2 INSIGHT AI ATTACHMENTS");
                      let fileName='';
                      let UUID='';
                      let type='';
                      if(this.device.platform=='browser') {
                        UUID = 'image-' + (new Date().getTime()).toString(16);
                        type = zone2AIImagePath.Image.substring(zone2AIImagePath.Image.indexOf("image/") + 6, zone2AIImagePath.Image.indexOf(';'))
                        fileName = UUID+'.'+type;
                      }
                      await this.dataService.createLMDAttachmentItem(tempFetch.data[0].LID,zone2AIImagePath,fileName,selectedRopeType);
                      for(let k=0;k<zone2TiledImages.length;k++) {
                        this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATING ROUTNIE INSPECTION ZONE2 INSIGHT AI TILED IMAGES ATTACHMENTS");
                        fileName = UUID+'-T'+(k+1)+'.'+type;
                        await this.dataService.createLMDAttachmentItem(tempFetch.data[0].LID,zone2TiledImages[k],fileName,selectedRopeType).then(res=>{
                          this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","CREATED ROUTNIE INSPECTION ZONE2 INSIGHT AI TAILED IMAGE"+k+"ATTACHMENT");
                        }).catch(err=>{
                          this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","ERROR CREATING ROUTNIE INSPECTION ZONE2 INSIGHT AI TAILED IMAGE"+k+"ATTACHMENT");
                        });
                      }
                    }
                    this.unviredSDK.logInfo("ROUTINE_INSPECTIONS_CERTIFICATES","SAVEINSPECTIONS","SUBMITTING LMD ATTACHMENTS");
                    var result = await this.submitLMD(tempFetch.data[0]);
                    if (tempFetch.data.length > 0) {
                      counter++;
                      if (result.type == ResultType.success) {
                      } else {
                        this.unviredSDK.logError("Cropping_lmd", "saveLMD", "Error: " + result.message)
                        error = result.message
                        // this.alertService.showAlert("", this.translate.instant("Error while inserting into DB"));
                      }
                    }
                  } else {
                    console.log(JSON.stringify(tempFetch))
                  }
                }
              } else {
                this.unviredSDK.logError("Cropping_lmd", "saveLMD", "Error: " + temp.error)
                this.alertService.showAlert("", this.translate.instant("Error while inserting into DB"));
              }
              if (counter == this.selectedMainlineList.length) {
                this.navCtrl.navigateBack('/routine-inspection-home');
                if(this.alertService.isLoading) {
                  this.alertService.dismiss();
                }
                this.dataService.refreshData();
                if (error.length > 0) {
                  this.showAlert("Error", error)
                } else {
                  this.alertService.presentToastRoutineInsp()
                }
              }
            }
          }
        }
      }
    } else if (this.selectedCertList.length > 0 && this.fieldSegment == 'tails') {
      var save = true;
      if (save == true) {
        if (!this.isEditLmd) {
          var counter = 0
          if (this.selectedCertList.some(ele => ele.INSPECTION_DATE === "")) {
            await this.alertService.showAlert("", this.translate.instant("Please fill date for all the inspections"));
          } else {
            await this.alertService.present();
            for (var ind = 0; ind < this.selectedCertList.length; ind++) {
              tempObject = this.selectedCertList[ind];
              tempObject.INSPECTION_DATE = typeof tempObject.INSPECTION_DATE == "object" ? moment(tempObject.INSPECTION_DATE.toISOString()).format('YYYY-MM-DD') : (typeof tempObject.INSPECTION_DATE == "string" ? moment(tempObject.INSPECTION_DATE).format('YYYY-MM-DD') : '')
              this.selectedAssetActivity.LMD_DATA = JSON.stringify(tempObject)
              this.selectedAssetActivity.LMD_ID = UtilserviceService.guid();
              this.selectedAssetActivity.EXTFLD3 = this.selectedCertList.length;
              var temp = await this.unviredSDK.dbInsert("LMD_HEADER", this.selectedAssetActivity, true);
              if (temp.type == ResultType.success) {
                if (!this.syncButtonClicked || counter < this.selectedCertList.length) {
                  this.syncButtonClicked = true;
                  var whereClause = "LMD_ID like '" + this.selectedAssetActivity.LMD_ID + "'"
                  var tempFetch = await this.unviredSDK.dbSelect(AppConstant.TABLE_LMD_HEADER, whereClause)
                  if (tempFetch.type == ResultType.success) {
                    if (tempFetch.data.length > 0) {
                      var result = await this.submitLMD(tempFetch.data[0])
                      counter++;
                      if (result.type == ResultType.success) {
                      } else {
                        this.unviredSDK.logError("Cropping_lmd", "saveLMD", "Error: " + result.message)
                        error = result.message
                        // this.alertService.showAlert("", this.translate.instant("Error while inserting into DB"));
                      }
                    }
                  } else {
                    console.log(JSON.stringify(tempFetch))
                  }
                }
              } else {
                this.unviredSDK.logError("Cropping_lmd", "saveLMD", "Error: " + temp.error)
                this.alertService.showAlert("", this.translate.instant("Error while inserting into DB"));
              }
              if (counter == this.selectedCertList.length) {
                this.alertService.dismiss();
                this.navCtrl.navigateBack('/routine-inspection-home');
                this.dataService.refreshData();
                if (error.length > 0) {
                  this.showAlert("Error", error)
                } else {
                  this.alertService.presentToastRoutineInsp()
                }
              }
            }
          }
        } else {
          var tempObject = JSON.parse(this.selectedAssetActivity.LMD_DATA)
          tempObject.winchType = this.fieldSegment
          tempObject.winchType = this.fieldSegment
          tempObject.winchType = this.fieldSegment
          tempObject.winches = this.selectedCertList
          this.selectedAssetActivity.LMD_DATA = JSON.stringify(tempObject)
          this.selectedAssetActivity.EXTFLD3 = this.selectedCertList.length;
          if (!this.isEditLmd) {
            var temp = await this.unviredSDK.dbInsert("LMD_HEADER", this.selectedAssetActivity, true);
            if (temp.type == ResultType.success) {
              setTimeout(async () => {
                if (!this.syncButtonClicked) {
                  this.syncButtonClicked = true;
                  var temp = await this.dataService.getSelectedLMDHeaderFromDb(this.selectedAssetActivity.LMD_ID)
                  if (temp.type == ResultType.success) {
                    if (temp.data.length > 0) {
                      var result = await this.submitLMD(temp.data[0])
                      this.alertService.dismiss()
                      this.navCtrl.navigateBack('/routine-inspection-home');
                      this.dataService.refreshData();
                      if (error.length > 0) {
                        this.showAlert("Error", error)
                      } else {
                        this.alertService.presentToastRoutineInsp()
                      }

                    }
                  } else {
                    console.log(JSON.stringify(temp))
                  }
                }
              }, 200);
            } else {
              this.unviredSDK.logError("Cropping_lmd", "saveLMD", "Error: " + temp.error)
              this.alertService.showAlert("", this.translate.instant("Error while inserting into DB"));
            }
          } else {
            var temp = await this.unviredSDK.dbInsertOrUpdate("LMD_HEADER", this.selectedAssetActivity, true);
            if (temp.type == ResultType.success) {
              setTimeout(async () => {
                if (!this.syncButtonClicked) {
                  this.syncButtonClicked = true;
                  var temp = await this.dataService.getSelectedLMDHeaderFromDb(this.selectedAssetActivity.LMD_ID)
                  if (temp.type == ResultType.success) {
                    if (temp.data.length > 0) {
                      var result = await this.submitLMD(temp.data[0])
                      this.alertService.dismiss()
                      this.router.navigate(['in-progress-lmd'])
                      this.dataService.refreshData();
                      this.alertService.presentEditedToast()
                    }
                  } else {
                    console.log(JSON.stringify(temp))
                  }
                }
              }, 200);
            } else {
              this.unviredSDK.logError("Cropping_lmd", "saveLMD", "Error: " + temp.error)
              this.alertService.showAlert("", this.translate.instant("Error while inserting into DB"));
            }
          }
        }
      } else {
        this.alertService.showAlert("", "Please select an equipment and fill details to continue")
      }
    } else {
      if (this.fieldSegment == 'mains') {
        this.alertService.showAlert("", "Please select certificate to continue")
      } else if (this.fieldSegment == 'tails') {
        this.alertService.showAlert("", "Please select a certificate to continue")
      }
    }
    // this.router.navigate(['configuration-line-information']);
  }

  async submitLMD(item) {
    // await this.alertService.present();
    let inputObject = {
      "LMD_HEADER": item
    }
    console.log("LID" + item.LID)
    let sendLmdToServer: any = {};
    let categoryLmd: any = {};
    if (this.device.platform == "browser") {
      this.unviredSDK.dbSaveWebData();
      categoryLmd['LMD_HEADER'] = item;
      sendLmdToServer['LMD'] = [categoryLmd];
      // return this.unviredSDK.syncForeground(RequestType.RQST, "", sendLmdToServer, AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, true)
      return this.unviredSDK.syncForeground(RequestType.RQST, inputObject, '',  AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, true)   
    } else {
      return this.unviredSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, "LMD", item.LID, false)
    }
  }

  // & TAILS - START
  async getRPSDetails() {
    console.log("test cert list")
    // let rpsResult = await this.unviredSDK.dbExecuteStatement(`SELECT RES.NAME AS NAME, PRODUCT, DIAM, RPS.NAME as ROPE, CERTIFICATE_NUM, RES.RPS as ROPE_ID, PRODUCT_LEN_METER   FROM ROPE_PRODUCT_SPEC_HEADER AS RPS, (
    //   SELECT RPS, NAME, PRODUCT, DIAM, CERTIFICATE_NUM, RPS , PRODUCT_LEN_METER from CERTIFICATE_HEADER where ASSET_ID = '${this.selectedAssetId}') AS RES
    //   where RPS.ID = RES.RPS ORDER BY NAME COLLATE NOCASE ASC`)
    let certRes = await this.unviredSDK.dbExecuteStatement(`SELECT A.*, EQUIPMENT_HEADER.EQUIP_NAME,EQUIPMENT_HEADER.SHIP_SIDE, EQUIPMENT_HEADER.INSTALLED_LOCATION FROM (SELECT CERTIFICATE_HEADER.NAME, CERTIFICATE_HEADER.CERTIFICATE_NUM, CERTIFICATE_HEADER.PRODUCT, CERTIFICATE_HEADER.DIAM, CERTIFICATE_HEADER.RPS, CERTIFICATE_HEADER.CONSTRUCTION, ROPE_PRODUCT_SPEC_HEADER.END_IN_USE, ROPE_PRODUCT_SPEC_HEADER.CURRENT_LENGTH_IN_METER, ROPE_PRODUCT_SPEC_HEADER.APPLICATION, ROPE_PRODUCT_SPEC_HEADER.EQUIP_DETAILS, ROPE_PRODUCT_SPEC_HEADER.WORKING_HRS, ROPE_PRODUCT_SPEC_HEADER.WORKING_OPERATIONS, ROPE_PRODUCT_SPEC_HEADER.RECORD_TYPE, ROPE_PRODUCT_SPEC_HEADER.NAME As RPS_NAME FROM CERTIFICATE_HEADER, ROPE_PRODUCT_SPEC_HEADER WHERE CERTIFICATE_HEADER.ASSET_ID =  '${this.selectedAssetId}' AND CERTIFICATE_HEADER.ACCOUNT_ID =  '${this.selectedAccount.ID}' AND CERTIFICATE_HEADER.RPS = ROPE_PRODUCT_SPEC_HEADER.ID AND ( ROPE_PRODUCT_SPEC_HEADER.APPLICATION like 'Mooring Tails'  OR  ROPE_PRODUCT_SPEC_HEADER.APPLICATION is NULL)) AS A LEFT JOIN EQUIPMENT_HEADER ON EQUIPMENT_HEADER.EQUIP_ID = A.EQUIP_DETAILS`)
    if (certRes.type == ResultType.success) {
      if (certRes.data.length > 0) {
        this.rpsList = certRes.data;
        for (var i = 0; i < this.rpsList.length; i++) {
          this.rpsList[i].CHECKED_ITEM = false;
          this.rpsList[i].CUSTOM_CONSTRUCTION = 'Tail'
        }
      } else {
        this.rpsList = []
      }
    } else {
      this.unviredSDK.logError("create-inspection", "filterCertificate", "Error while getting error from db" + JSON.stringify(this.mainlines))
    }
  }
  // & TAILS - END

  async presentModal(title: string, lid) {
    var tempList, pageTitle;
    switch (title) {
      case 'CERTIFICATE':
        tempList = this.rpsList
        pageTitle = this.translate.instant("Certificate")
        break;
      case 'MAINLINE':
        tempList = this.rpsList
        pageTitle = this.translate.instant("Certificate")
        break;
    }

    this.alertService.present().then(async () => {
      const modalCert = await this.modalController.create({
        component: GenericListPage,
        componentProps: { value: tempList, title: pageTitle, page: 'CERTIFICATE' }
      });
      await modalCert.present();

      modalCert.onDidDismiss().then(async (data) => {
        switch (title) {
          case 'CERTIFICATE':
            var ind = this.getLIDIndexMainline(lid);
            this.selectedMainlineList[ind].EQUIP_CERT = data.data.data.CERTIFICATE_NUM
            this.selectedMainlineList[ind].EQUIP_CERT_NAME = data.data.data.NAME
            this.selectedMainlineList[ind].EQUIP_RPS = data.data.data.RPS
            break;

          case 'MAINLINE':
            var ind = this.getLIDIndexMainline(lid);
            this.selectedMainlineList[ind].MAINLINE = data.data.data.CERTIFICATE_NUM
            this.selectedMainlineList[ind].MAINLINE_NAME = data.data.data.NAME
            break;
        }

      });
    })
  }

  checkIfSelectedMainline(lid) {
    if (this.selectedMainline != undefined) {
      var tempLid = this.selectedMainline.indexOf(lid);
      if (tempLid > -1) {
        // this.isDivOpen=true;
        return true
      } else {
        // this.isDivOpen=false;
        return false
      }
    } else {
      return false
    }
  }

  checkIfSelectedCert(lid) {
    if (this.selectedCerts != undefined) {
      var tempLid = this.selectedCerts.indexOf(lid);
      if (tempLid > -1) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  }

  getLIDIndexMainline(lid) {
    var lidIndex = this.selectedMainline.indexOf(lid);
    return lidIndex;
  }

  getLIDIndexCert(lid) {
    var lidIndex = this.selectedCerts.indexOf(lid);
    return lidIndex;
  }

  backButtonClicked() {
    var temp = JSON.parse(this.selectedAssetActivity.LMD_DATA)
    if (!this.isEditLmd) {
      if (this.selectedMainlineList.length > 0 || this.selectedCertList.length > 0) {
        this.router.navigate(['routine-inspection-certificates']);
        this.backAlertLMD(true);
      } else {
        if (temp.isGeneral == true) {
          this.navCtrl.navigateBack('/routine-inspection-home')
        } else {
          this.navCtrl.navigateBack('/routine-inspection-home')
        }
      }
    } else {
      if (!this.readOnly) {
        this.router.navigate(['routine-inspection-certificates']);
        this.backAlertLMD();
      } else {
        if (temp.isGeneral == true) {
          this.navCtrl.navigateBack('/routine-inspection-home')
        } else {
          this.navCtrl.navigateBack('/routine-inspection-home')
        }
      }
    }
  }

  async backAlertLMD(dirty?: boolean) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      message: '<strong>' + this.translate.instant('Do you want to discard the changes and go back') + '</strong>?',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: () => {
            var temp = JSON.parse(this.selectedAssetActivity.LMD_DATA)
            if (dirty == true) {
              if (temp.isGeneral == true) {
                this.navCtrl.navigateBack('/routine-inspection-home')
              } else {
                this.navCtrl.navigateBack('/routine-inspection-home')
              }
            } else {
              if (!this.isEditLmd) {
                this.navCtrl.navigateBack('/routine-inspection-home')
              } else {
                if (temp.isGeneral == true) {
                  this.navCtrl.navigateBack('/routine-inspection-home')
                } else {
                  this.navCtrl.navigateBack('/routine-inspection-home')
                }
              }
            }
          }
        }
      ]
    });
    await alert.present();
  }

  gotoResources() {
    if (this.device.platform == "browser") {
      this.dataService.gotoResources();
      return;
    }
    if (!this.isEditLmd) {
      this.utilityService.menuAlert('resources', 'resource')
    } else {

      if (!this.readOnly) {
        this.utilityService.menuAlert('resources', 'resource')
      } else {
        if (this.isFromInProgress == false) {
          this.router.navigate(['completed-lmd']);
        } else {
          this.router.navigate(['in-progress-lmd']);
        }
      }
    }

  }

  gotoInspections() {
    if (!this.isEditLmd) {
      this.utilityService.menuAlert('inspections', 'inspection-home')
    } else {
      if (!this.readOnly) {
        this.utilityService.menuAlert('inspections', 'inspection-home')
      } else {
        this.router.navigate(['detailed-routine-inspection']);
      }
    }
  }

  gotoHome() {
    if (!this.isEditLmd) {
      this.utilityService.menuAlert('home', 'home')
    } else {
      if (!this.readOnly) {
        this.utilityService.menuAlert('home', 'home')
      } else {
        this.router.navigate(['home']);
      }
    }

  }

  gotoContact() {
    if (this.device.platform == "browser") {
      this.dataService.gotoContact();
      return;
    }
    if (!this.isEditLmd) {
      this.utilityService.menuAlert('contact', 'contact')
    } else {
      if (!this.readOnly) {
        this.utilityService.menuAlert('contact', 'contact')
      } else {
        this.router.navigate(['contact']);
      }
    }
  }

  goToLineTracker() {
    if (!this.isEditLmd) {
      this.utilityService.menuAlert('lineTracker', 'line-tracker-home')
    } else {
      if (!this.readOnly) {
        this.utilityService.menuAlert('lineTracker', 'line-tracker-home')
      } else {
        this.dataService.navigateToLineTracker(this)
      }
    }

  }

  setSelectedCert(lid) {
    var ind = this.getLIDIndexMainline(lid);
    var selectedTail = this.selectedMainlineList[ind].SELECTED_TAIL
    this.selectedMainlineList[ind].TAIL_LENGTH = selectedTail.CURRENT_LENGTH_IN_METER
    this.selectedMainlineList[ind].EQUIP_CERT = selectedTail.CERTIFICATE_NUM
    this.selectedMainlineList[ind].EQUIP_CERT_NAME = selectedTail.NAME
    this.selectedMainlineList[ind].EQUIP_RPS = selectedTail.RPS
  }

  setSelectedLine(lid) {
    var ind = this.getLIDIndexCert(lid);
    var selectedTail = this.selectedMainlineList[ind].SELECTED_TAIL
    this.selectedMainlineList[ind].TAIL_LENGTH = selectedTail.CURRENT_LENGTH_IN_METER
  }

  compareTail(o1, o2) {
    return o1.RPS == o2.RPS
  }

  keyPressed(event: any, value: any, setEnding?: string) {
    console.log("key pressed")
    if (event.key != "Backspace") {
      if (value && value != null) {
        var tempStart = value.toString() + event.key;
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/.test(tempStart))) {
          console.log("key pressed" + value)
          return false;
        }
      } else {
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/).test(event.key)) {
          console.log("key pressed" + value)
          return false;
        }
      }
    }
  }

  keyPressedAbrasion(event: any, value: any, setEnding?: string) {
    console.log("key pressed")
    if (event.key != "Backspace") {
      if (value && value != null) {
        var tempStart = value.toString() + event.key;
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/.test(tempStart))) {
          console.log("key pressed" + value)
          return false;
        } else {
          if (event.key > 7) {
            return false;
          }
        }
      } else {
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/).test(event.key)) {
          console.log("key pressed" + value)
          return false;
        } if (event.key > 7) {
          return false;
        }
      }
    }
  }

  onChangeDisableJacketed(val, i) {
    if (val > 0) {
      this.selectedMainlineList[i].showJacketedError = true;
    } else {
      if (this.selectedMainlineList[i].ADDITIONAL_NUMBER_OF_JACKETS_REPAIR > 0 ||
        this.selectedMainlineList[i].ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES > 0 ||
        this.selectedMainlineList[i].ZONE_TWO_CUT_STRAND_COUNT_COVER > 0 ||
        this.selectedMainlineList[i].ZONE_ONE_CUT_STRAND_COUNT_COVER > 0) {
        this.selectedMainlineList[i].showJacketedError = true;
      } else {
        this.selectedMainlineList[i].showJacketedError = false;
      }
    }
  }

  onChangeWire(val, i) {
    if (val > 0) {
      this.selectedMainlineList[i].showWireError = true;
    } else {
      if (this.selectedMainlineList[i].ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION > 0 ||
        this.selectedMainlineList[i].ZONE_ONE_WAVINESS_GAP_MEASUREMENT > 0 ||
        this.selectedMainlineList[i].ZONE_ONE_MAX_WIRE_BREAKES > 0 ||
        this.selectedMainlineList[i].ZONE_TWO_WAVINESS_GAP_MEASUREMENT > 0 ||
        this.selectedMainlineList[i].ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION > 0) {
        this.selectedMainlineList[i].showWireError = true;
      } else {
        this.selectedMainlineList[i].showWireError = false;
      }
    }
  }

  onChangeHMPE(val, i) {
    if (val >= 0) {
      if (this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION >= 0 ||
        this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION >= 0 ||
        this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION >= 0 ||
        this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION >= 0 ||
        this.selectedMainlineList[i].ZONE_ONE_CUT_YARN_COUNT > 0 ||
        this.selectedMainlineList[i].ZONE_TWO_CUT_YARN_COUNT > 0 ||
        this.selectedMainlineList[i].ZONE_ONE_LENGTH_OF_GLAZING > 0 ||
        this.selectedMainlineList[i].ZONE_TWO_LENGTH_OF_GLAZING > 0 ||
        this.selectedMainlineList[i].TWIST_PER_METER > 0 ||
        this.selectedMainlineList[i].CHAFE_GEAR_HOLE_COUNT > 0) {
          if((this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION > 0 ||
            this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION > 0 || 
            this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION > 0 ||
            this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION > 0 ) &&

            (this.selectedMainlineList[i].ZONE_ONE_CUT_YARN_COUNT == 0 || this.selectedMainlineList[i].ZONE_ONE_CUT_YARN_COUNT == '') && 
            (this.selectedMainlineList[i].ZONE_TWO_CUT_YARN_COUNT == 0 || this.selectedMainlineList[i].ZONE_TWO_CUT_YARN_COUNT == '') &&
            (this.selectedMainlineList[i].ZONE_ONE_LENGTH_OF_GLAZING == 0 || this.selectedMainlineList[i].ZONE_ONE_LENGTH_OF_GLAZING == '' ) &&
            (this.selectedMainlineList[i].ZONE_TWO_LENGTH_OF_GLAZING == 0 || this.selectedMainlineList[i].ZONE_TWO_LENGTH_OF_GLAZING == '') &&
            (this.selectedMainlineList[i].TWIST_PER_METER == 0 || this.selectedMainlineList[i].TWIST_PER_METER == '') &&
            (this.selectedMainlineList[i].CHAFE_GEAR_HOLE_COUNT == 0 || this.selectedMainlineList[i].CHAFE_GEAR_HOLE_COUNT == '')) {
              let value = Math.max(this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION,
                this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION,
                this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION,
                this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION)
                if(value>5) {
                  this.selectedMainlineList[i].showExternalErrorMessageType = "RED";
                } else if(value>2 && value<=5) {
                  this.selectedMainlineList[i].showExternalErrorMessageType = "YELLOW";
                } else if(value<=2 && value>0){
                  this.selectedMainlineList[i].showExternalErrorMessageType = "GREEN";
                }
              this.selectedMainlineList[i].showHmpeError = false;
              this.selectedMainlineList[i].showExternalErrorMessage = true;
          //  if((this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION > 5 )||
              // (this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION > 5) ||
              // (this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION > 5) ||
              // (this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION > 5)) {
              //     this.selectedMainlineList[i].showExternalErrorMessageType = "RED";
              // }  else if((this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION > 2 && this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION <= 5 )||
              // (this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION > 2 && this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION <=5 ) ||
              // (this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION > 2 && this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION <=5 )||
              // (this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION > 2 && this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION <=5 )) {
              //     this.selectedMainlineList[i].showExternalErrorMessageType = "YELLOW";
              // } else if((this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION > 0 && this.selectedMainlineList[i].ZONE_ONE_EXTERNAL_ABRASION <= 2 )||
              // (this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION > 0 && this.selectedMainlineList[i].ZONE_TWO_EXTERNAL_ABRASION <=2 ) ||
              // (this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION > 0 && this.selectedMainlineList[i].ZONE_ONE_INTERNAL_ABRASION <= 2 )||
              // (this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION > 0 && this.selectedMainlineList[i].ZONE_TWO_INTERNAL_ABRASION <=2 )) {
              //     this.selectedMainlineList[i].showExternalErrorMessageType = "GREEN";
              // } 
            } else {
              this.selectedMainlineList[i].showHmpeError = true;
              this.selectedMainlineList[i].showExternalErrorMessage = false;
            }
      } else {
        this.selectedMainlineList[i].showHmpeError = false;
        this.selectedMainlineList[i].showExternalErrorMessage = false;
      }
    }
  }

  async onCheckedSekectAll(event:any) {
    if (this.fieldSegment == 'mains') {
      if(event.detail.checked) {
        if(this.allMainlinesSelectedFlag) {
          await this.alertService.preasentLoading()
          for (var i = 0; i < this.mainlines.length; i++) {
            if(this.mainlines[i].RECORD_TYPE == 'Competitor Product' && (this.mainlines[i].PRODUCT_TYPE!=null && this.mainlines[i].PRODUCT_TYPE!='HMSF (Class II)' && this.mainlines[i].PRODUCT_TYPE!='Steel')) {
              await this.toastService.present("bottom",this.translate.instant("Some of the Lines cannot be inspected, Please contact Samson for assistance"))
            } else {
              if (this.mainlines[i].CHECKED_ITEM == false) {
                this.mainlines[i].CHECKED_ITEM = true;
              }
            }
          }
          // this.mainlineSelected=-1;
        }
      } else {
        await this.alertService.preasentLoading()
        this.isDivOpen = false;
        for (var i = 0; i < this.mainlines.length; i++) {
          if (this.mainlines[i].CHECKED_ITEM == true) {
            this.mainlines[i].CHECKED_ITEM = false;
          }
        }
      }
    } else if (this.fieldSegment == 'tails') {
      if(event.detail.checked) {
        if(this.allTailsSelectedFlag) {
          await this.alertService.preasentLoading()
          for (var i = 0; i < this.rpsList.length; i++) {
            if (this.rpsList[i].CHECKED_ITEM == false) {
              this.rpsList[i].CHECKED_ITEM = true;
            }
          }
        }
      } else {
        await this.alertService.preasentLoading()
        this.isDivOpen = false;
        for (var i = 0; i < this.rpsList.length; i++) {
          if(this.rpsList[i].CHECKED_ITEM==true) {
            this.rpsList[i].CHECKED_ITEM = false;
          }
        }
      }
    }
    await this.alertService.dismiss();
  }

  onChangeTail(val, i) {
    if (val == 'Yes' && val == 'Damaged') {
      this.selectedCertList[i].showTailError = true;
    } else {
      if (this.selectedCertList[i].TAIL_FULLY_CUT_STRANDS == 'Yes' ||
        this.selectedCertList[i].TAIL_BEARING_POINT_CONNECTION == 'Damaged') {
        this.selectedCertList[i].showTailError = true;
      } else {
        this.selectedCertList[i].showTailError = false;
      }
    }
  }

  async getAnomalyList() {

    var wireAnomaly = await this.unviredSDK.dbSelect('WIRE_ANOMOLIES_HEADER', '')
    if (wireAnomaly.type == ResultType.success) {
      this.anomalyList = wireAnomaly.data;
      for (var i = 0; i < this.anomalyList.length; i++) {
        this.anomalyList[i].ANOMOLY_VALUE = '';
      }
    }
  }

  addEvent(type, event) {

  }

  preventCheckboxSelection(event:any) {
    event.stopPropagation();
    event.preventDefault();
  }

  async presentResultModal(props?:any) {
    return new Promise(async (resolve,reject)=>{
      const modalRes = await this.modalController.create({
        component: InsightAIRopeDetectionPage,
        backdropDismiss : false,
        cssClass: 'insightAIResultModal',
        componentProps: props
      });
      await modalRes.present();
      modalRes.onDidDismiss().then(async (data:any) => {
        resolve(data.data);
      }).catch(err=>{
        reject(err);
      });
    })
  }

  async openCamera(index:number,cert:any,zone?:string) {
    this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - OPENCAMERA METHOD START");
    // await this.alertService.presentLoaderWithMsg('Please wait.......');
    // this.visionAIService.screenMode = 'ROUTINE';
    let res = await this.cameraService.takePictureVisionAI("","RoutineAI").then(async result=>{
      if(result!=undefined) {
        this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - PRESENTING INSIGHT AI RESULTS MODAL START");
        this.visionAIService.selectedCertificate = cert;
        await this.presentResultModal({screenMode: "ROUTINEAI"}).then(async (data:any)=>{
          this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - PRESENTING INSIGHT AI RESULTS MODAL SUCCESS");
          if(data!=undefined) {
            if(data.action=='ACCEPT') {
              if(this.alertService.isLoading) {
                await this.alertService.dismiss();
              }

              if(zone=='ZONE1') {
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION = data.score;
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI = true;
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI_IMAGE = data.imageURL;
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_TILED_IMAGES = data.tiledImages;
                this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - WRITING ZONE1 INSIGHT AI ROUTINE INSPECTION IMAGE TO FILE STOAGE START");
                await this.cameraService.writeRoutineFileToStorage(data.imageURL,data.fileName+'.jpeg').then((path:any)=>{
                  this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - WRITING ZONE1 INSIGHT AI ROUTINE INSPECTION IMAGE TO FILE STOAGE COMPLETED");
                  // this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI_IMAGE = path.changingThisBreaksApplicationSecurity;
                  this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI_IMAGE_PATH = {"Image":path,mode:"insightAI","tileScore":data.score,"tiledImage":false};
                }).catch(err=>{
                  console.log(err);
                  this.alertService.showAlert("Error!","Error saving the image");
                  this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - ERROR WRITING ZONE1 INSIGHT AI ROUTINE INSPECTION IMAGE TO FILE STOAGE");
                });

                // this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI_IMAGE = this.cameraService.LMDAttatchments[];;
                this.onChangeHMPE(this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION, this.getLIDIndexMainline(cert.CERTIFICATE_NUM));
                if(this.alertService.isLoading) {
                  await this.alertService.dismiss();
                }
              } else if(zone=='ZONE2') {
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION = data.score;
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI = true;
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI_IMAGE = data.imageURL;
                this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_TILED_IMAGES = data.tiledImages;
                this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","WRITING ZONE2 INSIGHT AI ROUTINE INSPECTION IMAGE TO FILE STOAGE START");
                await this.cameraService.writeRoutineFileToStorage(data.imageURL,data.fileName+'.jpeg').then((path:any)=>{
                  this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - WRITING ZONE2 INSIGHT AI ROUTINE INSPECTION IMAGE TO FILE STOAGE COMPLETED");
                  // this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI_IMAGE = path.changingThisBreaksApplicationSecurity;
                  this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI_IMAGE_PATH = {"Image":path,mode:"insightAI","tileScore":data.score,"tiledImage":false};
                }).catch(err=>{
                  console.log(err);
                  this.alertService.showAlert("Error!","Error saving the image");
                  this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - ERROR WRITING ZONE2 INSIGHT AI ROUTINE INSPECTION IMAGE TO FILE STOAGE");
                });
                // this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI_IMAGE = this.cameraService.LMDAttatchments[];
                this.onChangeHMPE(this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION, this.getLIDIndexMainline(cert.CERTIFICATE_NUM));
                if(this.alertService.isLoading) {
                  await this.alertService.dismiss();
                }
              }
              await this.cameraService.updatePromptGuideStatus('routineAIFirst', false);
              if(this.alertService.isLoading) {
                await this.alertService.dismiss();
                this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - DISMISSING LOADER");
              }
            } else if(data.action=='RETAKE') {
              this.openCamera(index,cert,zone);
            } else if(data.action=='MANUAL') {
              if(zone=='ZONE1') {
                cert.ZONE_ONE_INSPECTION_TYPE = 'Manual';
                // Clear any existing AI data
                cert.ZONE_ONE_EXTERNAL_ABRASION = '';
                cert.ZONE_ONE_INSIGHT_AI = false;
                cert.ZONE_ONE_INSIGHT_AI_IMAGE = '';
                cert.ZONE_ONE_TILED_IMAGES = [];
                cert.ZONE_ONE_INSIGHT_AI_IMAGE_PATH = "";
              } else if(zone=='ZONE2') {
                cert.ZONE_TWO_INSPECTION_TYPE = 'Manual';
                // Clear any existing AI data
                cert.ZONE_TWO_EXTERNAL_ABRASION = '';
                cert.ZONE_TWO_INSIGHT_AI = false;
                cert.ZONE_TWO_INSIGHT_AI_IMAGE = '';
                cert.ZONE_TWO_TILED_IMAGES = [];
                cert.ZONE_TWO_INSIGHT_AI_IMAGE_PATH = "";
              }
            }
          }
        }).catch(err=>{
          this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - ERROR PRESENTING INSIGHT AI RESULTS MODAL:"+err);
        })
      }
    }).catch(err=>{
          console.log(err);
          this.unviredSDK.logInfo("ROUTINE_INSPECTION_CERTIFICATES","OPENCAMERA","ROUTINE_INSPECTION - ERROR IN TAKEPICTUREVISIONAI:"+err);
    });
  }

  async writeImageToStorage(imgURL:string,insightAIScore:number,fileName:string) {
    await this.cameraService.writeFileToStorage(imgURL,'ROUTINE_INSPECTION',insightAIScore,fileName);
  }

  async insightAiAlert() {
    var confAlert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      header: "Warning!",
      keyboardClose: true,
      message: "Switching to Manual will lose the Insight AI data, Are you sure?",
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary'
        }, {
          text: this.translate.instant('Ok'),
          role: 'continue'
        }
      ]
    });
    await confAlert.present();

    const { role }  = await confAlert.onDidDismiss();
    // this.unviredCordovaSDK.logError("CAMERA SERVICE","PRESENT ACTION SHEET","METHOD END");
    return role;
  }

  async visionTypeChanged(event:any,index:number,cert:any,zone:string) {
    console.log(event.target.value);
    if(event.target.value=='Manual') { // & if manual is selected
      if(zone=='ZONE1') {
        if(cert.ZONE_ONE_INSPECTION_TYPE=='VisionAI') {
          if((<HTMLInputElement>event.target).checked==true) {
            if(zone=='ZONE1' && cert.ZONE_ONE_EXTERNAL_ABRASION==='') {
              cert.ZONE_ONE_INSPECTION_TYPE = 'Manual';
              (<HTMLInputElement>event.target).checked=true;
            } else {
              (<HTMLInputElement>event.target).checked=false;
              await this.insightAiAlert().then(data=>{
                if(data=='cancel') {
                  console.log('cancel');
                  cert.ZONE_ONE_INSPECTION_TYPE = 'VisionAI';
                } else if(data=='continue') {
                  cert.ZONE_ONE_INSPECTION_TYPE = 'Manual';
                  (<HTMLInputElement>event.target).checked=true;
                  cert.ZONE_ONE_EXTERNAL_ABRASION = '';
                  cert.ZONE_ONE_INSIGHT_AI = false;
                  cert.ZONE_ONE_INSIGHT_AI_IMAGE = '';
                  cert.ZONE_ONE_TILED_IMAGES = [];
                  cert.ZONE_ONE_INSIGHT_AI_IMAGE_PATH = "";
                }
              });
            }
          }
        } else if (cert.ZONE_ONE_INSPECTION_TYPE=='Manual') {
          if((<HTMLInputElement>event.target).checked==false) {
            (<HTMLInputElement>event.target).checked=true;
          }
        }
      } else if(zone='ZONE2') {
        if(cert.ZONE_TWO_INSPECTION_TYPE=='VisionAI') {
          if((<HTMLInputElement>event.target).checked==true) {
            if(zone=='ZONE2' && cert.ZONE_TWO_EXTERNAL_ABRASION==='') {
              cert.ZONE_TWO_INSPECTION_TYPE = 'Manual';
              (<HTMLInputElement>event.target).checked=true;
            } else {
              (<HTMLInputElement>event.target).checked=false;
              await this.insightAiAlert().then(data=>{
                if(data=='cancel') {
                  console.log('cancel');
                } else if(data=='continue') {
                  cert.ZONE_TWO_INSPECTION_TYPE = 'Manual';
                  (<HTMLInputElement>event.target).checked=true;
                  cert.ZONE_TWO_EXTERNAL_ABRASION = '';
                  cert.ZONE_TWO_INSIGHT_AI = false;
                  cert.ZONE_TWO_INSIGHT_AI_IMAGE = '';
                  cert.ZONE_TWO_TILED_IMAGES = [];
                  cert.ZONE_TWO_INSIGHT_AI_IMAGE_PATH = "";
                }
              });
            }
          }
        } else if (cert.ZONE_TWO_INSPECTION_TYPE=='Manual') {
          if((<HTMLInputElement>event.target).checked==false) {
            (<HTMLInputElement>event.target).checked=true;
          }
        }
      }
    } else if(event.target.value=='VisionAI') { // & if Insight AI is selected
      if(zone=='ZONE1') {
        if(cert.ZONE_ONE_INSPECTION_TYPE=='Manual') {
          if((<HTMLInputElement>event.target).checked==true) {
            cert.ZONE_ONE_INSPECTION_TYPE = 'VisionAI';
            (<HTMLInputElement>event.target).checked=true;
            cert.ZONE_ONE_EXTERNAL_ABRASION = '';
            cert.ZONE_ONE_INSIGHT_AI = false;
            cert.ZONE_ONE_INSIGHT_AI_IMAGE = '';
            cert.ZONE_ONE_TILED_IMAGES = [];
            cert.ZONE_ONE_INSIGHT_AI_IMAGE_PATH = "";
          }
        } else if(cert.ZONE_ONE_INSPECTION_TYPE=='VisionAI') {
          if((<HTMLInputElement>event.target).checked==false) {
            (<HTMLInputElement>event.target).checked=true;
          }
        }
      } else if(zone=='ZONE2') {
        if(cert.ZONE_TWO_INSPECTION_TYPE=='Manual') {
          if((<HTMLInputElement>event.target).checked==true) {
            cert.ZONE_TWO_INSPECTION_TYPE = 'VisionAI';
            (<HTMLInputElement>event.target).checked=true;
            cert.ZONE_TWO_EXTERNAL_ABRASION = '';
            cert.ZONE_TWO_INSIGHT_AI = false;
            cert.ZONE_TWO_INSIGHT_AI_IMAGE = '';
            cert.ZONE_TWO_TILED_IMAGES = [];
            cert.ZONE_TWO_INSIGHT_AI_IMAGE_PATH = "";
            }
        } else if(cert.ZONE_TWO_INSPECTION_TYPE=='VisionAI') {
          if((<HTMLInputElement>event.target).checked==false) {
            (<HTMLInputElement>event.target).checked=true;
          }
        }
      }
    }
  }

  async previewInsightAIResult(index:number,cert?:any,zone?:string) {
    this.visionAIService.selectedCertificate = cert;
    if(zone=='ZONE1') {
      this.visionAIService.imageData = this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI_IMAGE;
      // this.router.navigate(['vision-rope-detection']);
      
    } else if(zone=='ZONE2') {
      this.visionAIService.imageData = this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI_IMAGE;
      // this.router.navigate(['vision-rope-detection']);
    }
    // this.router.navigate([''])
    let props={screenMode:'PREVIEWRESULTS',score:(zone=='ZONE1')?this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION:this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION}
    await this.presentResultModal(props).then((data:any)=>{
      if(data.action=='RETAKE') {
        if(zone=='ZONE1') {
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION = '';
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI = false;
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI_IMAGE = '';
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_TILED_IMAGES = [];
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI_IMAGE_PATH  = "";
        } else if(zone=='ZONE2') {
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION = '';
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI = false;
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI_IMAGE = '';
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_TILED_IMAGES = [];
          this.selectedMainlineList[this.getLIDIndexMainline(cert.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI_IMAGE_PATH  = "";
        }
        this.openCamera(index,cert,zone);
      } else if(data.action=='MANUAL') {
        if(zone=='ZONE1') {
          cert.ZONE_ONE_INSPECTION_TYPE = 'Manual';
          // Clear any existing AI data
          cert.ZONE_ONE_EXTERNAL_ABRASION = '';
          cert.ZONE_ONE_INSIGHT_AI = false;
          cert.ZONE_ONE_INSIGHT_AI_IMAGE = '';
          cert.ZONE_ONE_TILED_IMAGES = [];
          cert.ZONE_ONE_INSIGHT_AI_IMAGE_PATH = "";
        } else if(zone=='ZONE2') {
          cert.ZONE_TWO_INSPECTION_TYPE = 'Manual';
          // Clear any existing AI data
          cert.ZONE_TWO_EXTERNAL_ABRASION = '';
          cert.ZONE_TWO_INSIGHT_AI = false;
          cert.ZONE_TWO_INSIGHT_AI_IMAGE = '';
          cert.ZONE_TWO_TILED_IMAGES = [];
          cert.ZONE_TWO_INSIGHT_AI_IMAGE_PATH = "";
        }
      }
    }).catch(err=>{
      console.log(err);
    });
  }

  ionViewWillLeave() {
    // this.cropArea.close();
  }

}