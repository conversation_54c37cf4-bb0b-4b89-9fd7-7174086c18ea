import { Component, Input, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { RequestType, ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController, NavParams, MenuController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from 'src/app/services/alert.service';
import { VisionAIService } from 'src/app/services/vision-ai.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { AppConstant } from 'src/constants/appConstants';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faListCheck, faGrip, faEnvelope, faCircleInfo } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../../../environments/environment';
import { APP_SETTINGS_HEADER } from 'src/models/APP_SETTING_HEADER';
declare var cv: any; // * for opencv

@Component({
  selector: 'app-insight-ai-rope-detection',
  templateUrl: './insight-ai-rope-detection.page.html',
  styleUrls: ['./insight-ai-rope-detection.page.scss'],
})
export class InsightAIRopeDetectionPage implements OnInit {
  model: any = null;
  inputShape: any;
  imageData: any;
  predictions: any;
  // cropArea:any;
  // screenMode:any;
  tailedImages: any[] = [];
  tailedScores: any[] = [];
  originalImage: any;
  predictionImages: any[] = [];
  width: number;
  height: number;
  showhistogramImage: boolean = false; // * set this to control the visibility of histogram converted image
  showtailedImages: boolean = false; // * set this to control the visibility of tailed images
  showClaheAndTailedImages: boolean = false;
  scores: any[] = [];
  tiledImagesArr: any[] = [];
  originalRotatedImage: string;
  rootFileName: string;
  certObject: any;
  selectedRopeType: string = ''
  selectedCertificate: any;
  amsteelComparatorImages: { id: number, image: string }[] = [
    { id: 1, image: "./assets/img/External/<EMAIL>" },
    { id: 2, image: "./assets/img/External/<EMAIL>" },
    { id: 3, image: "./assets/img/External/<EMAIL>" },
    { id: 4, image: "./assets/img/External/<EMAIL>" },
    { id: 5, image: "./assets/img/External/<EMAIL>" },
    { id: 6, image: "./assets/img/External/<EMAIL>" },
    { id: 7, image: "./assets/img/External/<EMAIL>" }
  ];
  tenexComparatorImages: { id: number, image: string }[] = [
    { id: 1, image: "./assets/img/TenexExternal/External-Abrasion_1_Tenex.png" },
    { id: 2, image: "./assets/img/TenexExternal/External-Abrasion_2_Tenex.png" },
    { id: 3, image: "./assets/img/TenexExternal/External-Abrasion_3_Tenex.png" },
    { id: 4, image: "./assets/img/TenexExternal/External-Abrasion_4_Tenex.png" }
  ];

  @Input() screenMode: string = "QUICKINSPECT";
  @Input() score: any = null;
  @ViewChild('amsteelComparator', { static: true }) amsteelComparatorRef: ElementRef;
  @ViewChild('tenexComparator', { static: true }) tenexComparatorRef: ElementRef;
  @ViewChild('tiledImages', { static: true }) tiledImagesRef: ElementRef;
  @ViewChild('histogramOutput', { static: true }) histogramOutputRef: ElementRef;
  @ViewChild('ropeImage', { static: true }) ropeImageRef: ElementRef;

  insightAIcomplete: boolean = false;
  AImodelToSelect: string = ''
  showTenexComparator: boolean = false;
  showAmsteelComparator: boolean = false;
  // loadedImages = 0;
  constructor(
    private alertService: AlertService,
    public visionAIService: VisionAIService,
    public device: Device,
    private modalController: ModalController,
    // private translate:TranslateService,
    private menu: MenuController,
    private router: Router,
    private unviredCordovaSdk: UnviredCordovaSDK,
    private faIconLibrary: FaIconLibrary,
    private modalctrl: ModalController
  ) {
    this.faIconLibrary.addIcons(faBars, faListCheck, faGrip, faEnvelope, faCircleInfo)
  }

  async ngOnInit() {
    await this.unviredCordovaSdk.dbSelect("USER_HEADER", '').then(result => {
      console.log(result)
      if (result.data[0].EXTFLD1?.toLowerCase() != 'no') {
        this.visionAIService.showTilesAndSaveTilesEnabled = true;
      } else {
        this.visionAIService.showTilesAndSaveTilesEnabled = false;
      }
    }).catch(err => {
      console.log("err");
      this.unviredCordovaSdk.logInfo("INSIGHT-AI-ROPE-DETECTION", "ngoninit", "error reading the USER_HEADER table");
    })

    console.log("screenMode:", this.screenMode);
    if (!this.showhistogramImage) { //TODO:
      // document.getElementById("ropeHistogramImg").style.visibility = "hidden";
      // document.getElementById("output").style.display = "none";
    }

    switch (this.screenMode) {
      case "QUICKINSPECT":
        this.certObject = this.visionAIService.getQuickInspectionHeader()
        break;
      case "EXTERANLAI":
      case "ROUTINEAI":
      case "PREVIEWRESULTS":
        this.certObject = this.visionAIService.selectedCertificate;
        break;
    }
    console.log(this.certObject)
    if (this.screenMode == 'QUICKINSPECT' || this.screenMode=="PREVIEWRESULTS") {
      if (this.certObject.PRODUCT_NAME?.includes('AMSTEEL') || this.certObject.PRODUCT_NAME?.includes('ML-12') || this.certObject.PRODUCT?.includes('AMSTEEL') || this.certObject.PRODUCT?.includes('ML-12')) {
        this.selectedRopeType = 'Amsteel';
        this.AImodelToSelect = 'amsteel';
      } else if (this.certObject.PRODUCT_NAME?.includes('TENEX') || this.certObject.PRODUCT?.includes('TENEX')) {
        this.selectedRopeType = 'Tenex';
        this.AImodelToSelect = 'tenex';
      }
    } else {
      if ((this.certObject.PRODUCT_TYPE == 'HMSF (Class II)' || this.certObject.PRODUCT_TYPE == 'HMPE (Class II)') && this.certObject.CONSTRUCTION == "12-Strand") {
        this.selectedRopeType = 'Amsteel'
        this.AImodelToSelect = 'amsteel'
      } else if (this.certObject.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && this.certObject.CONSTRUCTION == "12-Strand") {
        this.selectedRopeType = 'Tenex';
        this.AImodelToSelect = 'amsteel'
      } else if ((this.certObject.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
        (this.certObject.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
        (this.certObject.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
        (this.certObject.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
        (this.certObject.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
        (this.certObject.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
        this.selectedRopeType = 'Amsteel'
        this.AImodelToSelect = 'amsteel'
      }
    }

    this.menu.enable(false, 'menu');
    // if(this.screenMode=="QUICKINSPECT") {
    await this.alertService.presentLoaderWithMsg("please wait...");
    // * initializing the opencv
    if (!this.visionAIService.isOpenCVInitialized) {
      await this.visionAIService.initializeOpenCv();
    }

    this.imageData = this.visionAIService.imageData;
    if (this.screenMode != "PREVIEWRESULTS") {
      console.log("original image first version:", this.imageData);
      let img = await document.getElementById("ropeSrcImg") as HTMLImageElement;
      // img.style.visibility = 'hidden';
      // document.getElementById('ropeImage').style.visibility = 'hidden'; 
      let srcImgage = new Image()
      srcImgage.setAttribute('src', this.imageData);
      srcImgage.onload = async () => {
        this.height = srcImgage.height;
        this.width = srcImgage.width;

        if (srcImgage.height < this.visionAIService.requiredModelHeight || srcImgage.width < this.visionAIService.requiredModelWidth) {
          if(this.alertService.isLoading) {
            this.alertService.dismiss();
          }
          let Alert;
          await this.alertService.showAndGetAlertModal('Warning!', "It appears to be image is not properly captured. Please retake the image and try again").then(alert => {
            Alert = alert;
          });
          await Alert.present();
          await Alert.onDidDismiss().then(async data => {
            if (this.screenMode == 'QUICKINSPECT') {
              this.router.navigate(['insight-AI-home'])
            } else {
              await this.modalController.dismiss({ action: 'RETAKE', score: null, imageURL: '' });
            }
          })
        } else {
          img.setAttribute("src", this.imageData);
          img.onload = () => {
            if ((img.width / img.height) > 1) {
              const canvas = document.createElement('canvas') as HTMLCanvasElement;
              let mat = cv.imread(img);
              cv.imshow(canvas, mat);

              let srcCanvas = document.createElement('canvas');
              let mat1 = cv.imread(srcCanvas);
              let rotatedMat = new cv.Mat();

              // Rotate the image by 90 degrees
              cv.transpose(mat, rotatedMat);
              cv.flip(rotatedMat, rotatedMat, 1); //* 1 represents horizontal flip (90-degree clockwise rotation)

              // Display the rotated image on the canvas
              cv.imshow(srcCanvas, rotatedMat);
              // const modifiedImageData = srcCanvas.toDataURL('image/jpeg', 1.0);
              const modifiedImageData = srcCanvas.toDataURL(); //& get base64 from the rotated canvas
              this.originalRotatedImage = modifiedImageData
              let dstImage = document.getElementById('ropeImage') as HTMLImageElement; //& get ropeImage image element
              dstImage.setAttribute('src', modifiedImageData); //& set base64 to the image element
              dstImage.onload = async () => {
                if (this.visionAIService.imageSource == 'camera') {
                  await this.predictInsightAIImage()
                } else if (this.visionAIService.imageSource == 'gallery') {
                  await this.predictInsightAIImage();
                }
              }
              mat.delete();
              mat1.delete();
              rotatedMat.delete();
              let temp = this.height;
              this.height = this.width
              this.width = temp;
              canvas.remove()
            } else {
              let dstImage = document.getElementById('ropeImage') as HTMLImageElement;
              dstImage.src = this.imageData;
              dstImage.onload = async () => {
                console.log(dstImage.height);
                console.log(dstImage.width);
                if (this.visionAIService.imageSource == 'camera') {
                  await this.predictInsightAIImage();
                } else if (this.visionAIService.imageSource == 'gallery') {
                  await this.predictInsightAIImage();
                }
              }
            }
          }
        }
      }

      // * below lines are to test the opencv js is loaded properly or not
      const srcMat = new cv.Mat(300, 300, cv.CV_8UC4);
      console.log(srcMat);

      // if (this.alertService.isLoading) {
      //   await this.alertService.dismiss();
      // }
    } else {
      if (this.screenMode == 'PREVIEWRESULTS') {
        let scrollContainer: any;
        let imageWidth: number;
        this.insightAIcomplete = true;
        switch (this.selectedRopeType) {
          case 'Amsteel':
            scrollContainer = this.amsteelComparatorRef.nativeElement;
            imageWidth = scrollContainer.children[0].width;
            this.showAmsteelComparator = true;
            this.showTenexComparator = false;
            break;
          case 'Tenex':
            scrollContainer = this.tenexComparatorRef.nativeElement;
            imageWidth = scrollContainer.children[0].width;
            this.showAmsteelComparator = false;
            this.showTenexComparator = true;
            break;
        }
        this.ropeImageRef.nativeElement.src = this.imageData;
        const imageContainer = this.ropeImageRef.nativeElement.parentElement;
        if (this.ropeImageRef.nativeElement.width < this.ropeImageRef.nativeElement.height) {
          this.ropeImageRef.nativeElement.classList.add('rotated');
          // Adjust container height for rotated image to minimize empty space
          if (imageContainer) {
            imageContainer.style.minHeight = '10rem';
            imageContainer.style.maxHeight = '10rem';
          }
        } else {
          this.ropeImageRef.nativeElement.classList.remove('rotated');
          // Reset container height for normal image
          if (imageContainer) {
            imageContainer.style.minHeight = '14rem';
            imageContainer.style.maxHeight = '14rem';
          }
        }
        // scrollContainer.style.scrollBehavior = 'smooth';
        scrollContainer.scrollLeft = 0;
        scrollContainer.scrollLeft += (imageWidth * (this.score - 1));
        if (this.alertService.isLoading) {
          await this.alertService.dismiss();
        }
      }
    }



  }

  async predictInsightAIImage() {
    console.log("Starting image prediction workflow:", new Date().toISOString());
    let ropeImage = document.getElementById("ropeImage") as HTMLImageElement;
    this.predictionImages = [];

    ropeImage.src = this.imageData;
    this.originalImage = this.imageData;

    ropeImage.onload = async () => {
      console.log("Original image loaded:", new Date().toISOString());
      // * rotate the image by 90 deg if the image is vertical/portrait image else leave it horizontal only
      const imageContainer = ropeImage.parentElement;
      if (ropeImage.width < ropeImage.height) {
        ropeImage.classList.add('rotated');
        // Adjust container height for rotated image to minimize empty space
        if (imageContainer) {
          imageContainer.style.minHeight = '10rem';
          imageContainer.style.maxHeight = '10rem';
        }
      } else {
        ropeImage.classList.remove('rotated');
        // Reset container height for normal image
        if (imageContainer) {
          imageContainer.style.minHeight = '14rem';
          imageContainer.style.maxHeight = '14rem';
        }
      }
      let tailedDiv = document.getElementById('tiledImages');
      while (tailedDiv.firstChild) {
        tailedDiv.removeChild(tailedDiv.firstChild);
      }
      if (!this.showtailedImages && !this.showClaheAndTailedImages) {
        tailedDiv.style.display = 'none';
      }
      let image = new Image()
      image.setAttribute('src', this.originalImage);
      let destImage;
      let claheInput;
      image.onload = async () => {
        console.log("Starting image processing:", new Date().toISOString());
        destImage = image;
        if ((image.width / image.height) > 1) { //* if the cropped image is horizontal then convert it to vertical one
          const canvas = document.createElement('canvas') as HTMLCanvasElement;
          let mat = cv.imread(image);
          cv.imshow(canvas, mat);

          let srcCanvas = document.createElement('canvas');
          let mat1 = cv.imread(srcCanvas);
          let rotatedMat = new cv.Mat();

          // Rotate the image by 90 degrees
          cv.transpose(mat, rotatedMat);
          cv.flip(rotatedMat, rotatedMat, 1); // 1 represents horizontal flip (90-degree clockwise rotation)

          // Display the rotated image on the canvas
          cv.imshow(srcCanvas, rotatedMat);

          let modifiedImageData = srcCanvas.toDataURL(); //& get base64 from the rotated canvas
          let dstImage = document.getElementById('ropeImage') as HTMLImageElement; //& get ropeImage image element
          dstImage.setAttribute('src', modifiedImageData); //& set base64 to the image element
          // dstImage.onload = async()=>{
          //   destImage = dstImage;
          // }
          await (new Promise((resolve, reject) => {
            dstImage.onload = () => {
              const imageContainer = dstImage.parentElement;
              if (dstImage.width < dstImage.height) {
                dstImage.classList.add('rotated');
                // Adjust container height for rotated image to minimize empty space
                if (imageContainer) {
                  imageContainer.style.minHeight = '10rem';
                  imageContainer.style.maxHeight = '10rem';
                }
              } else {
                dstImage.classList.remove('rotated');
                // Reset container height for normal image
                if (imageContainer) {
                  imageContainer.style.minHeight = '14rem';
                  imageContainer.style.maxHeight = '14rem';
                }
              }
              destImage = dstImage;
              resolve(true);
            };

            image.onerror = (error) => {
              reject(error);
            };
          }))
          mat.delete();
          mat1.delete();
          rotatedMat.delete();
          // let temp = this.height;
          // this.height =  this.width
          // this.width = temp;
        }
        // ********* this.predictionImages.push(image);
        await this.convertToGreyScale(); // * convert image to histogram quantization one
        let claheImage = await this.convertToTiledGreyScale(destImage); // ! check these two lines seems they are redudant
        let claheCanvas = document.getElementById('ropeHistogramImg') as HTMLCanvasElement;
        // claheCanvas.style.visibility = "hidden";
        let base = claheCanvas.toDataURL();
        if (this.visionAIService.showTilesAndSaveTilesEnabled) {
          await (new Promise((resolve, reject) => {
            let image = new Image();
            image.src = base;

            image.onload = () => {
              this.predictionImages.push(image); //* store the original clahe converted image to prediction array to store it in the file storage
              resolve(image);
            };

            image.onerror = (error) => {
              reject(error);
            };
          }))
        } else {
          this.predictionImages.push('')
        }

        claheInput = cv.imread(claheImage);
        let scaleFactor = claheInput.cols / 256; // & calculate scaling factor
        let cropheight = 256 * scaleFactor;
        console.log(scaleFactor);
        for (let i = 0; (cropheight * i) < claheInput.rows; i++) {
          let canvas = document.createElement('canvas') as HTMLCanvasElement;
          let dst = new cv.Mat();
          let rect;
          if (cropheight > claheInput.rows) {
            rect = new cv.Rect(0, 0, claheInput.cols, claheInput.rows); // * crop downwords
          } else {
            if ((cropheight * (i + 1)) < claheInput.rows) {
              rect = new cv.Rect(0, cropheight * i, claheInput.cols, cropheight); // * crop downwords
            } else {
              rect = new cv.Rect(0, (claheInput.rows - cropheight), claheInput.cols, cropheight); // * crop upwords
            }
          }
          dst = claheInput.roi(rect);
          cv.imshow(canvas, dst);
          dst.delete();


          let resizedCanvas = document.createElement('canvas');
          let newSrc = cv.imread(canvas);
          let newDst = new cv.Mat();
          let dsize = new cv.Size(256, 256);
          // You can try more different parameters
          cv.resize(newSrc, newDst, dsize, 0, 0, cv.INTER_AREA);
          cv.imshow(resizedCanvas, newDst)

          newDst.delete();
          newSrc.delete();
          let div = document.createElement('div');
          div.style.display = "flex";
          div.style.flexDirection = "column";
          div.style.alignItems = "center";
          div.appendChild(resizedCanvas)
          tailedDiv.appendChild(div);
          // if(this.showtailedImages) {

          //   tailedDiv.appendChild(resizedCanvas); // ! show tailed images in the UI
          // }

          // let dataUrl = canvas.toDataURL();
          let dataUrl = resizedCanvas.toDataURL();
          await (new Promise((resolve, reject) => {
            const image = new Image();
            image.src = dataUrl;
            // this.tiledImages.push({image:dataUrl:score)
            image.onload = () => {
              this.predictionImages.push(image); //* storing the tiles images to prediction array to run the inference on each of them
              resolve(image);
            };

            image.onerror = (error) => {
              reject(error);
            };
          }))
        }
        claheInput.delete();
        document.getElementById('ropeSrcImg').remove();
        await this.visionAIService.predictImagesStack(this.predictionImages, false, this.AImodelToSelect);
        // this.score = this.visionAIService.score;
        this.scores = this.visionAIService.scores;
        // this.scores = [7,7,7,2];
        // let min = Math.min.apply(null,this.scores);
        // let max = Math.max.apply(null,this.scores);
        let maxDiff = this.maxDifference(this.scores);
        if (this.scores.length == 1 || maxDiff <= 3) {
          if (this.scores.length == 1) {
            this.score = this.scores[0];
          } else {
            this.score = Math.round(this.scores.reduce((a, b) => a + b, 0) / this.scores.length);
          }
          this.visionAIService.score = this.score;
          if (this.score == 6) {
            this.score += 1;
            this.visionAIService.score = this.score;
          }
          let tiledImages = this.tiledImagesRef.nativeElement;
          for (let i = 0; i < tiledImages.children.length; i++) {
            let ele = tiledImages.children[i].children[0] as HTMLCanvasElement;
            let p = document.createElement('p');
            p.innerHTML = this.scores[i];
            tiledImages.children[i].appendChild(p);
            this.tiledImagesArr.push({ image: ele.toDataURL(), tileScore: this.scores[i], mode: 'insightAI', tiledImage: true })
          }

          this.insightAIcomplete = true;
          let scrollContainer;
          let imageWidth: number;
          // ! Scroll the image with respective score to center of the comparator
          if (this.selectedRopeType == 'Amsteel') {
            scrollContainer = this.amsteelComparatorRef.nativeElement;
            // this.tenexComparatorRef.nativeElement.remove();
            // scrollContainer.style.visibility = 'visible';
            imageWidth = scrollContainer.children[0].width;
            this.showAmsteelComparator = true;
            this.showTenexComparator = false;
          } else if (this.selectedRopeType == 'Tenex') {
            scrollContainer = this.tenexComparatorRef.nativeElement;
            // scrollContainer.style.visibility = 'visible';
            // this.amsteelComparatorRef.nativeElement.remove();
            imageWidth = scrollContainer.children[0].width;
            this.showAmsteelComparator = false;
            this.showTenexComparator = true;
          }
          scrollContainer.scrollLeft = 0;
          scrollContainer.scrollLeft += (imageWidth * (this.score - 1));
          if (this.alertService.isLoading) {
            await this.alertService.dismiss();
          }
          if (this.screenMode == 'QUICKINSPECT') {
            await this.submitQuickInspectionData()
          }
        } else {
          try {
            if (this.alertService.isLoading) {
              await this.alertService.dismiss();
            }
          } catch (err) {
            console.log(err)
          }
          let Alert;
          await this.alertService.showAndGetAlertModal('Warning!', "It appears to be image is not properly captured. Please retake the image and try again").then(alert => {
            Alert = alert;
          });
          await Alert.present();
          await Alert.onDidDismiss().then(async data => {
            if (this.screenMode == 'QUICKINSPECT') {
              this.router.navigate(['insight-AI-home'])
            } else {
              console.log("this is not insight AI page");
              await this.modalController.dismiss({ action: 'RETAKE', score: null, imageURL: '' });
            }
          })
        }

      }
    }
  }

  async submitQuickInspectionData() {
    await this.acceptScore(); //! writes tiles to storage to create attachments from
    let inspObj = this.visionAIService.quickInspectHeader;
    inspObj.RATING = this.score;
    let res = await this.unviredCordovaSdk.dbInsert(AppConstant.TABLE_QUICK_INSPECTION, inspObj, true);
    if (res.type == ResultType.success) {
      let inspRes = await this.unviredCordovaSdk.dbSelect(AppConstant.TABLE_QUICK_INSPECTION, `INSP_ID='${inspObj.INSP_ID}'`);
      if (inspRes.type == ResultType.success) {
        if (inspRes.data.length > 0) {
          let qckInspObj = inspRes.data[0];
          let fileName = '';
          let type = '';
          if (this.device.platform == 'browser') {
            await this.visionAIService.writeTiledImage(this.visionAIService.imageData, this.rootFileName, this.score, 'insightAI', false)
          } else {
            let base64 = this.visionAIService.imageData.split(',')[1];
            let blob = await this.visionAIService.b64toBlob(base64, 'image/jpeg');
            await this.visionAIService.writeTiledImage(blob, this.rootFileName + '.jpeg', this.score, 'insightAI', false)
          }

          if (this.device.platform == 'browser') {
            await this.visionAIService.createQuickInspectAttachments(this.visionAIService.tiledImages[this.visionAIService.tiledImages.length - 1], qckInspObj.LID, this.rootFileName + '.jpeg', this.selectedRopeType);
            this.visionAIService.tiledImages.pop();
          }

          for (let i = 0; i < this.visionAIService.tiledImages.length; i++) {
            fileName = this.rootFileName + '-T' + (i + 1) + '.' + type;
            await this.visionAIService.createQuickInspectAttachments(this.visionAIService.tiledImages[i], qckInspObj.LID, fileName, this.selectedRopeType);
          }

          let inputObject = {
            "QUICK_INSPECTION_HEADER": qckInspObj
          }
          if (this.device.platform == "browser") {
            this.unviredCordovaSdk.dbSaveWebData();
            return this.unviredCordovaSdk.syncForeground(RequestType.RQST, inputObject, '', AppConstant.ROPE_INSPECTIONS_PA_CREATE_QUICK_INSPECTION, true)
          } else {
            return this.unviredCordovaSdk.syncBackground(RequestType.RQST, inputObject, "", AppConstant.ROPE_INSPECTIONS_PA_CREATE_QUICK_INSPECTION, "QUICK_INSPECTION", qckInspObj.LID, false)
          }
        }
      }
    }
  }

  maxDifference(arr) {
    let maxDiff = 0
    if (arr.length == 1) {
      maxDiff = arr[0]
    } else {
      for (let i = 0; i < arr.length - 1; i++) {
        if (Math.abs(arr[i] - arr[i + 1]) > maxDiff) {
          maxDiff = Math.abs(arr[i] - arr[i + 1]);
        }
      }
    }
    return maxDiff;
  }

  async convertToGreyScale() {
    let imgElement = document.getElementById("ropeImage");
    let cvImage = cv.imread(imgElement);
    let canvasInput = document.createElement("canvas");
    cv.imshow(canvasInput, cvImage);
    cvImage.delete();
    let src = cv.imread(canvasInput);
    let equalDst = new cv.Mat();
    let claheDst = new cv.Mat();
    cv.cvtColor(src, src, cv.COLOR_RGBA2GRAY, 0);
    cv.equalizeHist(src, equalDst);
    let tileGridSize = new cv.Size(8, 8);
    let clahe = new cv.CLAHE(40, tileGridSize);
    clahe.apply(src, claheDst);
    cv.imshow("ropeHistogramImg", equalDst);
    cv.imshow("ropeHistogramImg", claheDst);
    src.delete();
    equalDst.delete();
    claheDst.delete();
    clahe.delete();
  }

  convertToTiledGreyScale(img: any) {
    let mat = cv.imread(img);
    let canvasInput = document.createElement("canvas");
    let canvasOutput = document.createElement("canvas");
    cv.imshow(canvasInput, mat);
    mat.delete();
    let src = cv.imread(canvasInput);
    let equalDst = new cv.Mat();
    let claheDst = new cv.Mat();
    cv.cvtColor(src, src, cv.COLOR_RGBA2GRAY, 0);
    cv.equalizeHist(src, equalDst);
    let tileGridSize = new cv.Size(8, 8);
    let clahe = new cv.CLAHE(40, tileGridSize);
    clahe.apply(src, claheDst);
    cv.imshow(canvasOutput, equalDst);
    cv.imshow(canvasOutput, claheDst);
    src.delete();
    equalDst.delete();
    claheDst.delete();
    clahe.delete();
    return canvasOutput;
  }

  async acceptScore() {
    // * write to storage and then return the path array
    this.visionAIService.tiledImages = [];
    let fileName = 'image-' + (new Date().getTime()).toString(16);
    this.rootFileName = fileName;
    try {
      let res =await this.visionAIService.writeTiledImageToStorage(this.tiledImagesArr, fileName)
      console.log("writeTiledImageToStorage result:", res);
      if(this.screenMode!= 'QUICKINSPECT') {
        this.modalController.dismiss({ action: 'ACCEPT', score: this.score, imageURL: this.visionAIService.imageData, tiledImages: this.visionAIService.tiledImages, fileName: fileName });
      }
    } catch(err) {
      console.log("error");
    }
  }

  async reatakePicture() {
    await this.alertService.showAlertModal('Warning!', "You will be taken back to inspection page and the current data will be discarded, Are you sure you want to continue").then(async role => {
      if (role == 'continue') {
        await this.modalController.dismiss({ action: 'RETAKE', score: null, imageURL: '' });
      }
    }).catch(err => {
      console.log(err);
    })
  }

  async manual() {
    await this.alertService.showAlertModal('Warning!', "You will be taken back to inspection page and the current data will be discarded, Are you sure you want to continue").then(role => {
      if (role == 'continue') {
        this.modalController.dismiss({ action: 'MANUAL', score: null, imageURL: '', switchToManual: true });
      }
    }).catch(err => {
      console.log(err);
    })
  }

  closeModal() {
    this.modalController.dismiss({});
  }

  viewTailedImages() {
    this.showClaheAndTailedImages = !this.showClaheAndTailedImages;
    if (this.showClaheAndTailedImages) {
      document.getElementById("ropeHistogramImg").style.visibility = "visible";
      document.getElementById("output").style.display = "flex";
      document.getElementById('tiledImages').style.display = 'grid';
    } else {
      document.getElementById("ropeHistogramImg").style.visibility = "hidden";
      document.getElementById("output").style.display = "none";
      document.getElementById('tiledImages').style.display = 'none';
    }
  }

  async scrollLeft() {
    let scrollContainer;
    let imageWidth
    if (this.selectedRopeType == 'Amsteel') {
      scrollContainer = this.amsteelComparatorRef.nativeElement;
      // scrollContainer.style.visibility = 'visible';
      imageWidth = scrollContainer.children[0].width;
    } else if (this.selectedRopeType == 'Tenex') {
      scrollContainer = this.tenexComparatorRef.nativeElement;
      // scrollContainer.style.visibility = 'visible';
      imageWidth = scrollContainer.children[0].width;
    }
    let scroll;
    if (scrollContainer.scrollLeft % imageWidth == 0) {
      scroll = imageWidth;
    } else {
      if (scrollContainer.scrollLeft > imageWidth) {
        scroll = scrollContainer.scrollLeft % imageWidth;
      } else {
        scroll = scrollContainer.scrollLeft;
      }
    }
    scrollContainer.scrollLeft -= scroll;
  }

  async scrollRight() {
    let scrollContainer;
    let imageWidth
    if (this.selectedRopeType == 'Amsteel') {
      scrollContainer = this.amsteelComparatorRef.nativeElement;
      // scrollContainer.style.visibility = 'visible';
      imageWidth = scrollContainer.children[0].width;
    } else if (this.selectedRopeType == 'Tenex') {
      scrollContainer = this.tenexComparatorRef.nativeElement;
      // scrollContainer.style.visibility = 'visible';
      imageWidth = scrollContainer.children[0].width;
    }
    let scroll;
    if (scrollContainer.scrollLeft % imageWidth == 0) {
      scroll = imageWidth
    } else {
      if (scrollContainer.scrollLeft > imageWidth) {
        scroll = imageWidth - (scrollContainer.scrollLeft % imageWidth);
      } else {
        scroll = (scrollContainer.scrollLeft % imageWidth) - imageWidth;
      }
    }
    scrollContainer.scrollLeft += scroll;
  }

  cancel() {
    this.modalctrl.dismiss(null, 'cancel');
  }

  async ionViewWillLeave() {
    this.menu.enable(true, 'menu');
    this.score = null;
    this.visionAIService.resetAll()

    let settingsObject = new APP_SETTINGS_HEADER();
    settingsObject.KEY_FLD = 'insightAIFirst';
    settingsObject.VALUE = 'false';
    let res = await this.unviredCordovaSdk.dbInsertOrUpdate("APP_SETTINGS_HEADER", settingsObject, true);
    if (res.type == ResultType.success) {
      console.log("Updated insightAIFirst in APP_SETTINGS_HEADER:", res.data);
    } else {
      console.error("Error updating insightAIFirst in APP_SETTINGS_HEADER:", res.message);
    }

    // ! delete the attachments of insightAI
    if(this.screenMode=="QUICKINSPECT") {
      await this.visionAIService.deleteInsightAIImages();
    }
  }

}

