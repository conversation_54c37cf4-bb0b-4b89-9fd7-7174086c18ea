import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, NavigationExtras } from '@angular/router';
import { MenuController, AlertController, Platform, ToastController, ActionSheetController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from '../services/alert.service';
import { CameraService } from '../services/camera.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { Camera, CameraOptions } from '@awesome-cordova-plugins/camera/ngx';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faList<PERSON><PERSON><PERSON>, faGrip } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-abrasion-comparator-external',
  templateUrl: './abrasion-comparator-external.page.html',
  styleUrls: ['./abrasion-comparator-external.page.scss'],
})
export class AbrasionComparatorExternalPage implements OnInit {

  imageData: any;
  component: any;

  constructor(
    public helpService: HelpService,
    public translate: TranslateService,
    public device: Device,
    public router: Router,
    private menu: MenuController,
    public domSanitizer: DomSanitizer,
    public screenOrientation: ScreenOrientation, 
    private service: UtilserviceService,
    public cameraService: CameraService,
    public dataService: DataService,
    public alertService: AlertService,
    public alertController: AlertController,
    public platform: Platform,
    public toastController: ToastController,
    public actionSheetController: ActionSheetController,
    private camera: Camera,
    public faIconLibrary : FaIconLibrary) {

    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip)
  }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  gotoHome() {
    this.router.navigate(['home'])
  }

  async gotoInspections() {
    this.router.navigate(['inspection-home']);
  }

  gotoResources() {
    this.router.navigate(['resource'])
  }

  gotoContact() {
    this.router.navigate(['contact'])
  }

  back() {
    this.router.navigate(['abrasion-comparator']);
  }

  takePicture() {

    var that = this;
    if (this.device.platform == 'windows') {
      this.presentActionSheet().then((dismissed) => {
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          this.camera.getPicture(options).then((imageData) => {
            let realData = imageData;
            let blob = this.b64toBlob(realData, 'image/jpeg');
            var imgURL = URL.createObjectURL(blob);
            this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
          }, (err) => {
            console.log(err);
          });
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            sourceType: this.camera.PictureSourceType.SAVEDPHOTOALBUM,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          this.camera.getPicture(options).then((imageData) => {
            console.log("IMAGEDATD ++++++++++" + imageData)
            let realData = imageData;
            let blob = this.b64toBlob(realData, 'image/jpeg');
            var imgURL = URL.createObjectURL(blob);
            this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
          }, (err) => {
            console.log(err);
          });
        } else {
          console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
      })
    } else if (this.device.platform == 'browser') {
      this.presentActionSheet().then((dismissed) => {
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          const options: CameraOptions = {
            quality: 50,
            targetWidth: 640,
            targetHeight: 480,
            destinationType: this.camera.DestinationType.FILE_URI,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          this.camera.getPicture(options).then((imageData) => {
            imageData = "data:image/jpeg;base64," + imageData
            this.imageData = imageData;
          }, (err) => {
            console.log(err);
          });
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          document.getElementById('myInput').click();
        } else {
          console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
      })
    } else if (this.platform.is("android")) {
      this.presentActionSheet().then((dismissed) => {
        console.log(dismissed);
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          this.cameraService.setComparatorPage(that);
          this.unlockScreen();
          var extras : NavigationExtras = { state: {currentPage: this.component}}
          this.router.navigate(['camera-comparator']);
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          let cameraOptions: CameraOptions = {
            quality: 100,
            destinationType: this.camera.DestinationType.DATA_URL,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE,
            sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
          }

          this.camera.getPicture(cameraOptions).then((imageData: string) => {
          let realData = imageData;
          let blob = this.b64toBlob(realData, 'image/jpeg');
          var imgURL = URL.createObjectURL(blob);
          this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
          });
          // this.imagePicker.getPictures(options).then((results) => {
          //   for (var i = 0; i < results.length; i++) {
          //       console.log('Image URI: ' + results[i]);
          //   }
          //   this.imageData = imageData;

          // }, (err) => {
          //   console.log(err)
          // });
        } else {
          // this.cameraMode = ''
          // console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        // this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        // this.zone.run(() => {
        //   return ""
        // })
      })
    } else {
      this.presentActionSheet().then((dismissed) => {
        console.log(dismissed);
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          let options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          this.camera.getPicture(options).then((imageData: string) => {
            let realData = imageData;
            let blob = this.b64toBlob(realData, 'image/jpeg');
            var imgURL = URL.createObjectURL(blob);
            this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
          });
          //   this.cameraMode = dismissed.role
          //   this.unlockScreen();
          // this.router.navigate(['camera-comparator']);
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          let cameraOptions: CameraOptions = {
            quality: 100,
            destinationType: this.camera.DestinationType.DATA_URL,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE,
            sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,

          }

          this.camera.getPicture(cameraOptions).then((imageData: string) => {
            let realData = imageData;
            let blob = this.b64toBlob(realData, 'image/jpeg');
            var imgURL = URL.createObjectURL(blob);
            this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
          });
          // this.imagePicker.getPictures(options).then((results) => {
          //   for (var i = 0; i < results.length; i++) {
          //       console.log('Image URI: ' + results[i]);
          //   }
          //   this.imageData = imageData;

          // }, (err) => {
          //   console.log(err)
          // });
        } else {
          // this.cameraMode = ''
          // console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        // this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        // this.zone.run(() => {
        //   return ""
        // })
      })

    }
  }

  async presentActionSheet() {
    const actionSheet = await this.actionSheetController.create({
      cssClass: 'my-custom-class',
      buttons: [{
        text: 'Camera',
        icon: 'camera',
        handler: () => {
          actionSheet.dismiss(1, 'camera');
        }
      }, {
        text: 'Gallery',
        icon: 'image',
        handler: () => {
          actionSheet.dismiss(2, 'gallery');
        }
      }]
    });
    await actionSheet.present();

    let dataOnDismissed = await actionSheet.onDidDismiss();
    return dataOnDismissed;
  }

  onFileSelected(event) {
    console.log(event);
    var filepath = URL.createObjectURL(event.target.files[0])
    var mimeType = event.target.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      var message = "Only images are supported.";
      return;
    }
    var reader = new FileReader();
    var imagePath = event.target.files;
    reader.readAsDataURL(event.target.files[0]);
    reader.onload = (_event) => {
      let imgURL = reader.result;
      console.log(imgURL)
      this.imageData = imgURL
    }
  }

  b64toBlob(b64Data, contentType) {
    contentType = contentType || '';
    var sliceSize = 512;
    var byteCharacters = atob(b64Data);
    var byteArrays = [];

    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);

      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      var byteArray = new Uint8Array(byteNumbers);

      byteArrays.push(byteArray);
    }

    var blob = new Blob(byteArrays, {type: contentType});
    return blob;
  }

  unlockScreen() {
    this.screenOrientation.unlock();
  }

  setImageData() {
    let realData = this.cameraService.getComparator();
    let blob = this.b64toBlob(realData, 'image/jpeg');
    var imgURL = URL.createObjectURL(blob);
    this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }
}